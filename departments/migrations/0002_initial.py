# Generated by Django 5.2 on 2025-06-03 12:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("departments", "0001_initial"),
        ("organizations", "0001_initial"),
    ]

    operations = [
        migrations.AddField(
            model_name="department",
            name="organization",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="departments",
                to="organizations.organization",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="department",
            unique_together={("name", "organization")},
        ),
    ]
