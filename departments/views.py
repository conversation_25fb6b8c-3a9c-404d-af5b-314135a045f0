from django.shortcuts import render
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.views import APIView

from jechspace_backend.utils import ErrorCodes, api_response
from organizations.mixins import OrganizationPermissionMixin
from permissions.constants import PermissionActions, PermissionCategories

from .models import Department
from .serializers import DepartmentSerializer


class DepartmentListCreateView(OrganizationPermissionMixin, APIView):
    """
    API view for listing and creating departments
    Uses the new permission system with HasResourcePermission
    """

    permission_category = PermissionCategories.DEPARTMENT
    permission_action = PermissionActions.VIEW

    @swagger_auto_schema(
        operation_summary="List organization departments",
        operation_description="Lists all departments in the specified organization.",
        responses={
            200: openapi.Response(
                description="Departments retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Departments retrieved successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    "id": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="550e8400-e29b-41d4-a716-446655440000",
                                    ),
                                    "name": openapi.Schema(
                                        type=openapi.TYPE_STRING, example="Engineering"
                                    ),
                                    "organization": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="550e8400-e29b-41d4-a716-446655440000",
                                    ),
                                    "description": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="Engineering department",
                                        nullable=True,
                                    ),
                                    "created_at": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        format="date-time",
                                        example="2023-01-01T12:00:00Z",
                                    ),
                                    "updated_at": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        format="date-time",
                                        example="2023-01-01T12:00:00Z",
                                    ),
                                },
                            ),
                        ),
                    },
                ),
            ),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Departments"],
    )
    def get(self, request, organization_id):
        """
        List all departments in an organization
        """
        # Organization object is already available from HasResourcePermission
        departments = Department.objects.filter(organization=request.organization)
        serializer = DepartmentSerializer(departments, many=True)

        return api_response(
            data=serializer.data,
            message="Departments retrieved successfully",
            status_code=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Create organization department",
        operation_description="Creates a new department in the specified organization.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["name"],
            properties={
                "name": openapi.Schema(type=openapi.TYPE_STRING, example="Engineering"),
                "description": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    example="Engineering department",
                    nullable=True,
                ),
            },
        ),
        responses={
            201: openapi.Response(
                description="Department created successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Department created successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="Engineering"
                                ),
                                "organization": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "description": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="Engineering department",
                                    nullable=True,
                                ),
                                "created_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                                "updated_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Invalid data"),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Departments"],
    )
    def post(self, request, organization_id):
        """
        Create a new department in an organization
        """
        # Override permission action for POST requests
        self.permission_action = PermissionActions.CREATE

        data = request.data.copy()
        data["organization_id"] = organization_id

        serializer = DepartmentSerializer(data=data, context={"request": request})
        if serializer.is_valid():
            serializer.save()
            return api_response(
                data=serializer.data,
                message="Department created successfully",
                status_code=status.HTTP_201_CREATED,
            )
        return api_response(
            errors=serializer.errors,
            message="Validation error",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
        )


class DepartmentDetailView(OrganizationPermissionMixin, APIView):
    """
    API view for retrieving, updating and deleting a department
    """

    permission_category = PermissionCategories.DEPARTMENT
    permission_action = PermissionActions.VIEW

    @swagger_auto_schema(
        operation_summary="Get department details",
        operation_description="Retrieves details of a specific department in the organization.",
        responses={
            200: openapi.Response(
                description="Department retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Department retrieved successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="Engineering"
                                ),
                                "organization": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "description": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="Engineering department",
                                    nullable=True,
                                ),
                                "created_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                                "updated_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                            },
                        ),
                    },
                ),
            ),
            404: openapi.Response(description="Department not found"),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Departments"],
    )
    def get(self, request, organization_id, department_id):
        """
        Retrieve a department
        """
        try:
            department = Department.objects.get(
                id=department_id, organization_id=organization_id
            )
            serializer = DepartmentSerializer(department)
            return api_response(
                data=serializer.data,
                message="Department retrieved successfully",
                status_code=status.HTTP_200_OK,
            )
        except Department.DoesNotExist:
            return api_response(
                errors={
                    "department": {
                        "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                        "message": "Department not found",
                    }
                },
                message="Department not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )

    @swagger_auto_schema(
        operation_summary="Update department",
        operation_description="Updates a specific department in the organization.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "name": openapi.Schema(
                    type=openapi.TYPE_STRING, example="Engineering Team"
                ),
                "description": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    example="Updated engineering department description",
                    nullable=True,
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Department updated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Department updated successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="Engineering Team"
                                ),
                                "organization": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "description": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="Updated engineering department description",
                                    nullable=True,
                                ),
                                "created_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                                "updated_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Invalid data"),
            404: openapi.Response(description="Department not found"),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Departments"],
    )
    def patch(self, request, organization_id, department_id):
        """
        Update a department
        """
        try:
            department = Department.objects.get(
                id=department_id, organization_id=organization_id
            )
            data = request.data.copy()
            data["organization_id"] = organization_id

            serializer = DepartmentSerializer(
                department, data=data, partial=True, context={"request": request}
            )
            if serializer.is_valid():
                serializer.save()
                return api_response(
                    data=serializer.data,
                    message="Department updated successfully",
                    status_code=status.HTTP_200_OK,
                )
            return api_response(
                errors=serializer.errors,
                message="Validation error",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
            )
        except Department.DoesNotExist:
            return api_response(
                errors={
                    "department": {
                        "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                        "message": "Department not found",
                    }
                },
                message="Department not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )

    @swagger_auto_schema(
        operation_summary="Delete department",
        operation_description="Deletes a specific department in the organization. Cannot delete departments with members.",
        responses={
            204: openapi.Response(description="Department deleted successfully"),
            400: openapi.Response(description="Cannot delete department with members"),
            404: openapi.Response(description="Department not found"),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Departments"],
    )
    def delete(self, request, organization_id, department_id):
        """
        Delete a department
        """
        try:
            department = Department.objects.get(
                id=department_id, organization_id=organization_id
            )
            # Check if department has members
            if department.members.exists():
                return api_response(
                    errors={
                        "department": {
                            "code": "department_has_members",
                            "message": "Cannot delete a department with members. Reassign members first.",
                        }
                    },
                    message="Cannot delete department with members",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                )

            department.delete()
            return api_response(
                message="Department deleted successfully",
                status_code=status.HTTP_204_NO_CONTENT,
            )
        except Department.DoesNotExist:
            return api_response(
                errors={
                    "department": {
                        "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                        "message": "Department not found",
                    }
                },
                message="Department not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )
