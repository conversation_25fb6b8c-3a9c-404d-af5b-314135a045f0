from django.db import models
from core.models import BaseModel
from organizations.models import Organization


class Department(BaseModel):
    """
    Department model representing a department within an organization.
    Departments can have specific permissions and users assigned to them.
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name="departments"
    )

    def __str__(self):
        return f"{self.name} - {self.organization.name}"

    class Meta:
        unique_together = ("name", "organization")
