from rest_framework import serializers
from jechspace_backend.utils import ErrorCodes
from users.serializers import UserOrganizationSerializer
from organizations.models import Organization
from .models import Department


class DepartmentSerializer(serializers.ModelSerializer):
    """
    Serializer for the Department model
    """

    organization_id = serializers.UUIDField(required=True)
    members_count = serializers.SerializerMethodField(read_only=True)
    members = UserOrganizationSerializer(many=True, read_only=True)

    class Meta:
        model = Department
        fields = [
            "id",
            "name",
            "description",
            "organization_id",
            "members_count",
            "members",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "created_at",
            "updated_at",
            "members_count",
            "members",
        ]

    def get_members_count(self, obj):
        return obj.members.count()

    def validate_organization_id(self, value):
        """
        Ensure the organization exists and the user has permission to create departments
        """
        try:
            Organization.objects.get(id=value)
            return value
        except Organization.DoesNotExist:
            raise serializers.ValidationError(
                "Organization not found.", code=ErrorCodes.ORGANIZATION_NOT_FOUND
            )

    def validate(self, data):
        # Ensure department name is unique within this organization
        name = data.get("name")
        organization_id = data.get("organization_id")

        if Department.objects.filter(
            name=name, organization_id=organization_id
        ).exists():
            raise serializers.ValidationError(
                {
                    "name": "A department with this name already exists in this organization."
                },
                code=ErrorCodes.DEPARTMENT_NAME_EXISTS,
            )

        return data

    def create(self, validated_data):
        organization_id = validated_data.pop("organization_id")
        organization = Organization.objects.get(id=organization_id)
        validated_data["organization"] = organization
        return super().create(validated_data)
