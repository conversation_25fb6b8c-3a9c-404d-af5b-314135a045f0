from django.urls import path
from . import views
from rest_framework_simplejwt.views import TokenRefreshView


urlpatterns = [
    # Authentication endpoints
    path("signup/", views.SignupView.as_view(), name="signup"),
    path("signin/", views.LoginView.as_view(), name="login"),
    path("signout/", views.LogoutView.as_view(), name="logout"),

    # Email verification (merged view)
    path("email/verification/", views.EmailVerificationView.as_view(), name="email_verification"),

    # Password management (merged view)
    path("password/reset/", views.PasswordResetView.as_view(), name="password_reset"),
    path("password/", views.ChangePasswordView.as_view(), name="change_password"),

    # Token management
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
]
