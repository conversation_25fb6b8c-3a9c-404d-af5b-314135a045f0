import uuid

from users.models import UserToken


def generate_verification_token(user):
    """
    Generate a unique token for email verification

    Args:
        user: The user to create token for

    Returns:
        String token
    """
    # Create a token entry in the database
    token = str(uuid.uuid4())

    # Store token in the database associated with this user
    UserToken.objects.create(
        user=user,
        token=token,
    )

    return token
