from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.utils import timezone
from django.utils.text import slugify
from django.db import IntegrityError

from rest_framework import serializers
from rest_framework_simplejwt.tokens import RefreshToken

from jechspace_backend.utils import ErrorCodes
from organizations.models import Organization
from users.models import Roles, User, UserOrganization, UserToken, UserType

# Common email providers that should not be used for organization accounts
COMMON_EMAIL_DOMAINS = [
    "gmail.com",
    "yahoo.com",
    "hotmail.com",
    "outlook.com",
    "aol.com",
    "icloud.com",
    "mail.com",
    "protonmail.com",
    "zoho.com",
    "yandex.com",
    "live.com",
    "msn.com",
    "me.com",
    "gmx.com",
]


class SignupSerializer(serializers.ModelSerializer):
    email = serializers.EmailField(
    required=True,
    error_messages={
        "invalid": "Enter a valid email address.",
        "blank": "Email address is required.",
    },
    )
    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )
    confirm_password = serializers.CharField(write_only=True, required=True)
    user_type = serializers.ChoiceField(
        choices=UserType.CHOICES, default=UserType.INDIVIDUAL
    )

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "password",
            "confirm_password",
            "first_name",
            "last_name",
            "phone_number",
            "profession",
            "is_verified",
            "user_type",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "is_verified", "created_at", "updated_at"]

    def validate_email(self, value):
        if User.objects.filter(email=value).exists():
            raise serializers.ValidationError(
                "A user with this email already exists",
                code=ErrorCodes.EMAIL_ALREADY_EXISTS,
            )

        # Check if organization owner is using a common email domain
        user_type = self.initial_data.get("user_type")
        if user_type == UserType.ORGANIZATION_OWNER:
            domain = value.split("@")[-1].lower()
            if domain in COMMON_EMAIL_DOMAINS:
                raise serializers.ValidationError(
                    "Organization owners must use a company email address, not a common email provider",
                    code=ErrorCodes.COMMON_EMAIL_DOMAIN,
                )

        return value

    def validate(self, data):
        if data["password"] != data["confirm_password"]:
            raise serializers.ValidationError(
                "confirm_password and password do not match",
                code=ErrorCodes.PASSWORDS_DO_NOT_MATCH,
            )

        return data

    def create(self, validated_data):
        validated_data.pop("confirm_password", None)
        try:
            user = User.objects.create_user(**validated_data)
            return user
        except IntegrityError:
            raise serializers.ValidationError({
                "email":{
                    "message": "Email already exists",
                    "code": ErrorCodes.EMAIL_ALREADY_EXISTS,
                }
            })



class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(write_only=True, required=True)

    def validate(self, data):
        user = authenticate(email=data.get("email"), password=data.get("password"))

        if not user:
            raise serializers.ValidationError(
                "Invalid email or password", code=ErrorCodes.INVALID_CREDENTIALS
            )

        if not user.is_verified:
            raise serializers.ValidationError(
                "Please verify your email before logging in",
                code=ErrorCodes.ACCOUNT_NOT_VERIFIED,
            )

        # Generate JWT tokens
        refresh = RefreshToken.for_user(user)
        return {
            "user": {
                "id": user.id,
                "email": user.email,
                "first_name": user.first_name,
                "last_name": user.last_name,
            },
            "auth": {
                "access_token": str(refresh.access_token),
                "refresh_token": str(refresh),
                "token_type": "Bearer",
                "expires_in": refresh.access_token.lifetime.total_seconds(),
            },
        }


class RequestVerificationSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)

    def validate_email(self, value):
        try:
            user = User.objects.get(email=value)
            if user.is_verified:
                raise serializers.ValidationError(
                    "This account is already verified",
                    code=ErrorCodes.ACCOUNT_ALREADY_VERIFIED,
                )
            self.user = user
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError(
                "No account found with this email address",
                code=ErrorCodes.EMAIL_NOT_FOUND,
            )


class VerifyUserSerializer(serializers.Serializer):
    token = serializers.CharField(required=True)

    def validate_token(self, value):
        try:
            token_obj = UserToken.objects.get(
                token=value, used=False, expires_at__gt=timezone.now()
            )
            self.user = token_obj.user
            self.token_obj = token_obj
            return value
        except UserToken.DoesNotExist:
            raise serializers.ValidationError(
                "Invalid or expired verification token", code=ErrorCodes.INVALID_TOKEN
            )


class RequestPasswordResetSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)

    def validate_email(self, value):
        try:
            user = User.objects.get(email=value)
            self.user = user
            return value
        except User.DoesNotExist:
            raise serializers.ValidationError(
                "No account found with this email address",
                code=ErrorCodes.EMAIL_NOT_FOUND,
            )


class ResetPasswordSerializer(serializers.Serializer):
    password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )
    confirm_password = serializers.CharField(write_only=True, required=True)

    def validate(self, data):
        if data["password"] != data["confirm_password"]:
            raise serializers.ValidationError(
                {"confirm_password": "Passwords do not match"},
                code=ErrorCodes.PASSWORDS_DO_NOT_MATCH,
            )

        try:
            token_obj = UserToken.objects.get(
                token=self.context["token"], used=False, expires_at__gt=timezone.now()
            )
            self.user = token_obj.user
            self.token_obj = token_obj
        except UserToken.DoesNotExist:
            raise serializers.ValidationError(
                "Invalid or expired reset token", code=ErrorCodes.INVALID_TOKEN
            )

        return data

    def save(self):
        self.user.set_password(self.validated_data["password"])
        self.user.save()
        self.token_obj.used = True
        self.token_obj.save()


class ChangePasswordSerializer(serializers.Serializer):
    current_password = serializers.CharField(write_only=True, required=True)
    new_password = serializers.CharField(
        write_only=True, required=True, validators=[validate_password]
    )
    confirm_password = serializers.CharField(write_only=True, required=True)

    def validate_current_password(self, value):
        user = self.context["user"]
        if not user.check_password(value):
            raise serializers.ValidationError(
                "Current password is incorrect", code=ErrorCodes.INVALID_PASSWORD
            )
        return value

    def validate(self, data):
        if data["new_password"] != data["confirm_password"]:
            raise serializers.ValidationError(
                {"confirm_password": "Passwords do not match"},
                code=ErrorCodes.PASSWORDS_DO_NOT_MATCH,
            )
        return data

    def save(self):
        user = self.context["user"]
        user.set_password(self.validated_data["new_password"])
        user.save()
