from django.utils import timezone

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.views import APIView
from rest_framework_simplejwt.token_blacklist.models import (
    BlacklistedToken,
    OutstandingToken,
)
from rest_framework_simplejwt.tokens import RefreshToken

from events.constants import EventTypes
from events.events import events
from jechspace_backend.utils import ErrorCodes, api_response
from users.models import UserToken, UserType
from users.utils import generate_user_token

from .serializers import (
    ChangePasswordSerializer,
    LoginSerializer,
    RequestPasswordResetSerializer,
    RequestVerificationSerializer,
    ResetPasswordSerializer,
    SignupSerializer,
    VerifyUserSerializer,
)


class SignupView(APIView):
    """
    API view for user registration
    """

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_summary="Register a new user",
        operation_description="Create a new user account with the provided information.",
        request_body=SignupSerializer,
        manual_parameters=[
            openapi.Parameter(
                "type",
                openapi.IN_QUERY,
                description="User type - 'individual' or 'organization'",
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            201: openapi.Response(
                description="User registered successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="User registered successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "email": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="<EMAIL>"
                                ),
                                "first_name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="John"
                                ),
                                "last_name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="Doe"
                                ),
                                "phone_number": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="+1234567890",
                                    nullable=True,
                                ),
                                "profession": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="Software Engineer",
                                ),
                                "is_verified": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=False
                                ),
                                "user_type": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="individual"
                                ),
                                "created_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                                "updated_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Invalid input or validation error",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="Validation Error"
                        ),
                        "errors": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "email": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="A user with this email already exists",
                                    ),
                                ),
                                "password": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="Password must be at least 8 characters",
                                    ),
                                ),
                            },
                        ),
                    },
                ),
            ),
        },
        tags=["Authentication"],
    )
    def post(self, request):
        # Get the 'type' query parameter and convert to user_type
        user_type = request.query_params.get("type", "individual")

        # Map user-friendly 'type' parameter to internal user_type values
        user_type_mapping = {
            "individual": UserType.INDIVIDUAL,
            "organization": UserType.ORGANIZATION_OWNER,
        }

        # Get the appropriate user_type value or default to individual
        user_type_value = user_type_mapping.get(user_type, UserType.INDIVIDUAL)

        # Add user_type to request data
        request_data = request.data.copy()
        request_data["user_type"] = user_type_value

        serializer = SignupSerializer(data=request_data)
        if serializer.is_valid():
            try:
                user = serializer.save()
                # Generate verification token
                token = generate_user_token(user, UserToken.Purpose.EMAIL_VERIFICATION)
                events.emit(
                    EventTypes.SEND_VERIFICATION_CODE, {"user": user, "token": token}
                )

                # Prepare response message based on user type
                message = "User registered successfully"
                if user.user_type == UserType.ORGANIZATION_OWNER:
                    message = "Organization owner registered successfully"

                return api_response(
                    data=serializer.data,
                    message=message,
                    status_code=status.HTTP_201_CREATED,
                )
            except Exception as e:
                return api_response(
                    success=False,
                    errors={"error": {
                        "code": "UNKNOWN_ERROR",  
                        "message": str(e) 
                 }},
                    message="Registration failed",
                    status_code=status.HTTP_400_BAD_REQUEST,
                )
        return api_response(
            errors=serializer.errors,
            message="Validation Error",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
        )


class LoginView(APIView):
    """
    API view for user login
    """

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_summary="User login",
        operation_description="Authenticate a user with email and password.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["email", "password"],
            properties={
                "email": openapi.Schema(
                    type=openapi.TYPE_STRING, example="<EMAIL>"
                ),
                "password": openapi.Schema(
                    type=openapi.TYPE_STRING, example="securepassword"
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Login successful",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="Login successful"
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "user": openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    properties={
                                        "id": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="550e8400-e29b-41d4-a716-446655440000",
                                        ),
                                        "email": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="<EMAIL>",
                                        ),
                                        "first_name": openapi.Schema(
                                            type=openapi.TYPE_STRING, example="John"
                                        ),
                                        "last_name": openapi.Schema(
                                            type=openapi.TYPE_STRING, example="Doe"
                                        ),
                                    },
                                ),
                                "auth": openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    properties={
                                        "access_token": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                                        ),
                                        "refresh_token": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
                                        ),
                                        "token_type": openapi.Schema(
                                            type=openapi.TYPE_STRING, example="Bearer"
                                        ),
                                        "expires_in": openapi.Schema(
                                            type=openapi.TYPE_INTEGER, example=1800
                                        ),
                                    },
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Invalid credentials"),
            403: openapi.Response(description="Account not verified"),
        },
        tags=["Authentication"],
    )
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            return api_response(
                data=serializer.validated_data,
                message="Login successful",
                status_code=status.HTTP_200_OK,
            )

        # Handle different error types
        status_code = (
            status.HTTP_403_FORBIDDEN
            if serializer.errors.get("detail")
            and serializer.errors.get("detail")[0].code
            == ErrorCodes.ACCOUNT_NOT_VERIFIED
            else status.HTTP_400_BAD_REQUEST
        )

        return api_response(
            errors=serializer.errors,
            message="Authentication failed",
            status_code=status_code,
            success=False,
        )


class LogoutView(APIView):
    """
    API view for user logout
    """

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_summary="User logout",
        operation_description="Invalidate refresh tokens to prevent issuing new access tokens. Note: existing access tokens will remain valid until they expire.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "refresh_token": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Optional: specific refresh token to invalidate",
                ),
            },
            required=[],
        ),
        responses={
            200: openapi.Response(
                description="Logged out successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="Logged out successfully"
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Invalid token or error during logout"),
            401: openapi.Response(
                description="Authentication credentials were not provided"
            ),
        },
        tags=["Authentication"],
    )
    def post(self, request):
        try:
            refresh_token = request.data.get("refresh_token")
            if refresh_token:
                token = RefreshToken(refresh_token)
                token.blacklist()
            else:
                # Blacklist all tokens for the user
                tokens = OutstandingToken.objects.filter(user_id=request.user.id)
                for token in tokens:
                    BlacklistedToken.objects.get_or_create(token=token)

            return api_response(
                message="Logged out successfully",
                status_code=status.HTTP_200_OK,
            )
        except Exception as e:
            return api_response(
                success=False,
                errors=str(e),
                message="Logout failed",
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class EmailVerificationView(APIView):
    """
    Handles both requesting a verification link (POST) and verifying a user (PATCH)
    """

    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_summary="Request email verification",
        operation_description="Sends a verification email with a link to verify the user's email address.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["email"],
            properties={
                "email": openapi.Schema(
                    type=openapi.TYPE_STRING, example="<EMAIL>"
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Verification link sent",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Verification link sent to your email",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "email": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="<EMAIL>"
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Invalid email or account already verified"
            ),
        },
        tags=["Email Verification"],
    )
    def post(self, request):
        serializer = RequestVerificationSerializer(data=request.data)
        if serializer.is_valid():
            # Invalidate all existing unused tokens
            UserToken.objects.filter(
                user=serializer.user,
                purpose=UserToken.Purpose.EMAIL_VERIFICATION,
                used=False,
                expires_at__gt=timezone.now(),
            ).update(used=True)

            # Generate new verification token
            token = generate_user_token(
                serializer.user, UserToken.Purpose.EMAIL_VERIFICATION
            )
            events.emit(
                EventTypes.SEND_VERIFICATION_CODE,
                {"user": serializer.user, "token": token},
            )

            return api_response(
                data={"email": serializer.user.email},
                message="Verification link sent to your email",
                status_code=status.HTTP_200_OK,
            )
        return api_response(
            errors=serializer.errors,
            message="Invalid request",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
        )

    @swagger_auto_schema(
        operation_summary="Verify email with token",
        operation_description="Verifies a user's email address using the provided token.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["token"],
            properties={
                "token": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    example="2f9e3a1b-5d8c-4e7f-9a2b-8c1d3e4f5a6b",
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Email verification successful",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Email verification successful",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "is_verified": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=True
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Invalid or expired token"),
        },
        tags=["Email Verification"],
    )
    def patch(self, request):
        serializer = VerifyUserSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.user
            token_obj = serializer.token_obj

            # Mark user as verified
            user.is_verified = True
            user.save()

            # Mark token as used
            token_obj.used = True
            token_obj.save()

            return api_response(
                data={"is_verified": True},
                message="Email verification successful",
                status_code=status.HTTP_200_OK,
            )

        return api_response(
            errors=serializer.errors,
            message="Invalid verification token",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
        )


class PasswordResetView(APIView):
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_summary="Request password reset",
        operation_description="Sends a password reset email with a link to reset the user's password.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["email"],
            properties={
                "email": openapi.Schema(
                    type=openapi.TYPE_STRING, example="<EMAIL>"
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Password reset instructions sent",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Password reset instructions sent to your email",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "email": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="<EMAIL>"
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Invalid email or account not found"),
        },
        tags=["Password Management"],
    )
    def post(self, request):
        serializer = RequestPasswordResetSerializer(data=request.data)
        if serializer.is_valid():
            token = generate_user_token(
                serializer.user, UserToken.Purpose.PASSWORD_RESET
            )
            events.emit(
                EventTypes.SEND_PASSWORD_RESET_CODE,
                {"user": serializer.user, "token": token},
            )
            return api_response(
                data={"email": serializer.user.email},
                message="Password reset instructions sent to your email",
                status_code=status.HTTP_200_OK,
            )
        return api_response(
            errors=serializer.errors,
            message="Invalid request",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
        )

    @swagger_auto_schema(
        operation_summary="Reset password with token",
        operation_description="Resets a user's password using the provided token and new password.",
        manual_parameters=[
            openapi.Parameter(
                "token",
                openapi.IN_QUERY,
                description="Password reset token",
                type=openapi.TYPE_STRING,
                required=True,
            )
        ],
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["password", "confirm_password"],
            properties={
                "password": openapi.Schema(
                    type=openapi.TYPE_STRING, example="new_secure_password"
                ),
                "confirm_password": openapi.Schema(
                    type=openapi.TYPE_STRING, example="new_secure_password"
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Password reset successful",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Password reset successful",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "email": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="<EMAIL>"
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Invalid token, expired token, or password validation error"
            ),
        },
        tags=["Password Management"],
    )
    def patch(self, request):
        token = request.GET.get("token")
        if not token:
            return api_response(
                message="Reset token is required",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "token": {
                        "code": ErrorCodes.REQUIRED_FIELD_MISSING,
                        "message": "Reset token is required",
                    }
                },
            )
        serializer = ResetPasswordSerializer(
            data=request.data, context={"token": token}
        )
        if serializer.is_valid():
            serializer.save()
            return api_response(
                data={"email": serializer.user.email},
                message="Password reset successful",
                status_code=status.HTTP_200_OK,
            )
        return api_response(
            errors=serializer.errors,
            message="Invalid request",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
        )


class ChangePasswordView(APIView):
    """
    API view for changing password (authenticated)
    """

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_summary="Change password (authenticated)",
        operation_description="Allows an authenticated user to change their password.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["current_password", "new_password", "confirm_password"],
            properties={
                "current_password": openapi.Schema(
                    type=openapi.TYPE_STRING, example="current_password"
                ),
                "new_password": openapi.Schema(
                    type=openapi.TYPE_STRING, example="new_secure_password"
                ),
                "confirm_password": openapi.Schema(
                    type=openapi.TYPE_STRING, example="new_secure_password"
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Password changed successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Password changed successfully",
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Invalid current password, passwords don't match, or password validation error"
            ),
            401: openapi.Response(
                description="Authentication credentials were not provided"
            ),
        },
        tags=["Password Management"],
    )
    def patch(self, request):
        serializer = ChangePasswordSerializer(
            data=request.data, context={"user": request.user}
        )
        if serializer.is_valid():
            serializer.save()
            return api_response(
                message="Password changed successfully",
                status_code=status.HTTP_200_OK,
            )
        return api_response(
            errors=serializer.errors,
            message="Invalid request",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
        )
