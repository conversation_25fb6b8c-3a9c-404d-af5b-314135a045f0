from django.contrib.auth import get_user_model
from django.db import transaction

from rest_framework import serializers

from departments.models import Department
from jechspace_backend.utils import ErrorCodes
from users.models import Roles, UserOrganization

from .models import EMPLOYEE_SIZE_CHOICES, Organization

User = get_user_model()


class ContactSerializer(serializers.Serializer):
    """Serializer for organization contact information"""

    phone = serializers.CharField(max_length=20)
    email = serializers.EmailField()
    website = serializers.URLField()


class OrganizationStatsSerializer(serializers.Serializer):
    """Serializer for organization statistics"""

    total_members = serializers.IntegerField(read_only=True)
    total_locations = serializers.IntegerField(read_only=True, default=0)


class UserInfoSerializer(serializers.ModelSerializer):
    """Serializer for user information in organization responses"""

    name = serializers.SerializerMethodField()
    role = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = ["id", "name", "email", "role"]

    def get_name(self, obj):
        return obj.get_full_name()

    def get_role(self, obj):
        # Get role from context if available
        user_org = self.context.get("user_org")
        if user_org:
            return user_org.role
        return None


class OrganizationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating organizations"""

    contact = ContactSerializer()
    employee_size = serializers.ChoiceField(choices=EMPLOYEE_SIZE_CHOICES)

    class Meta:
        model = Organization
        fields = [
            "name",
            "slug",
            "description",
            "type",
            "contact",
            "industry",
            "employee_size",
        ]

    def validate_slug(self, value):
        """Ensure slug is unique"""
        if Organization.objects.filter(slug=value).exists():
            raise serializers.ValidationError(
                "Organization with this slug already exists.",
                code=ErrorCodes.ORGANIZATION_SLUG_EXISTS,
            )

        return value

    def validate(self, data):
        user = self.context.get("request").user
        if user.owned_organization.all().exists():
            raise serializers.ValidationError(
                "User already owns an organization",
                code=ErrorCodes.USER_ALREADY_OWNS_AN_ORGANIZATION,
            )

        return data

    @transaction.atomic
    def create(self, validated_data):
        contact_data = validated_data.pop("contact")
        validated_data.update(contact_data)

        user = self.context["request"].user

        organization = Organization.objects.create(owner=user, **validated_data)

        UserOrganization.objects.create(
            user=user, organization=organization, role=Roles.OWNER
        )

        return organization


class OrganizationUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating organizations"""

    contact = ContactSerializer(required=False)
    logo = serializers.ImageField(required=False)

    class Meta:
        model = Organization
        fields = [
            "name",
            "slug",
            "description",
            "type",
            "logo",
            "contact",
            "industry",
            "employee_size",
        ]

    def validate_slug(self, value):
        """Ensure slug is unique (excluding current instance)"""
        if self.instance and self.instance.slug == value:
            return value
        if Organization.objects.filter(slug=value).exists():
            raise serializers.ValidationError(
                "Organization with this slug already exists.",
                code=ErrorCodes.ORGANIZATION_SLUG_EXISTS,
            )
        return value

    def update(self, instance, validated_data):
        contact_data = validated_data.pop("contact", None)

        # Update contact fields if provided
        if contact_data:
            for field, value in contact_data.items():
                setattr(instance, field, value)

        # Update other fields
        for field, value in validated_data.items():
            setattr(instance, field, value)

        instance.save()
        return instance


class OrganizationListSerializer(serializers.ModelSerializer):
    """Serializer for organization list views"""

    contact = ContactSerializer(source="*")
    user = UserInfoSerializer(source="owner", read_only=True)
    stats = OrganizationStatsSerializer(source="*", read_only=True)

    class Meta:
        model = Organization
        fields = [
            "id",
            "name",
            "slug",
            "description",
            "type",
            "logo",
            "contact",
            "industry",
            "created_at",
            "user",
            "stats",
        ]

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # Add stats
        data["stats"] = {
            "total_members": instance.users.count(),
            "total_locations": instance.locations.count(),
        }

        # Add user role if available from context
        request = self.context.get("request")
        if request and request.user.is_authenticated:
            user_org = instance.users.filter(user=request.user).first()
            if user_org and data["user"]:
                data["user"]["role"] = user_org.role

        return data


class OrganizationDetailSerializer(serializers.ModelSerializer):
    """Serializer for detailed organization view"""

    contact = ContactSerializer(source="*")
    stats = OrganizationStatsSerializer(source="*", read_only=True)

    class Meta:
        model = Organization
        fields = [
            "id",
            "name",
            "slug",
            "description",
            "type",
            "logo",
            "industry",
            "created_at",
            "contact",
            "stats",
        ]

    def to_representation(self, instance):
        data = super().to_representation(instance)

        data["stats"] = {
            "total_members": instance.users.count(),
            "total_locations": instance.locations.count(),
        }

        return data


class PublicOrganizationSerializer(serializers.ModelSerializer):
    """Serializer for public organization listings"""

    contact = ContactSerializer(source="*")
    stats = OrganizationStatsSerializer(source="*", read_only=True)
    is_member = serializers.SerializerMethodField()
    has_pending_request = serializers.SerializerMethodField()

    class Meta:
        model = Organization
        fields = [
            "id",
            "name",
            "slug",
            "description",
            "logo",
            "industry",
            "stats",
            "contact",
            "is_member",
            "has_pending_request",
        ]

    def get_is_member(self, obj):
        """Check if current user is a member of this organization"""
        request = self.context.get("request")
        if request and request.user.is_authenticated:
            return obj.users.filter(user=request.user).exists()
        return False

    def get_has_pending_request(self, obj):
        """Check if current user has pending join request"""
        # Will be implemented when join requests are added
        return False

    def to_representation(self, instance):
        data = super().to_representation(instance)

        # Add stats
        data["stats"] = {
            "total_members": instance.users.count(),
            "total_locations": instance.locations.count(),
        }

        return data


class LeaveOrganizationSerializer(serializers.Serializer):
    """Serializer for leaving organization"""

    user_email = serializers.EmailField()

    def validate_user_email(self, value):
        """Validate that user exists"""
        try:
            User.objects.get(email=value)
        except User.DoesNotExist:
            raise serializers.ValidationError(
                "User with this email does not exist.", code=ErrorCodes.USER_NOT_FOUND
            )
        return value


class OrganizationMemberSerializer(serializers.ModelSerializer):
    """Serializer for organization members"""

    user = UserInfoSerializer(read_only=True)
    joined_at = serializers.DateTimeField(source="created_at", read_only=True)

    class Meta:
        model = UserOrganization
        fields = ["user", "role", "department", "joined_at"]

    def to_representation(self, instance):
        data = super().to_representation(instance)
        print(data)

        if data["user"]:
            user_data = data.pop("user")
            data.update(
                {
                    "id": user_data["id"],
                    "email": user_data["email"],
                    "first_name": instance.user.first_name,
                    "last_name": instance.user.last_name,
                    "profile_picture": instance.user.profile_picture.url
                    if instance.user.profile_picture
                    else None,
                    "profession": instance.user.profession,
                }
            )

        if data["department"]:
            department = Department.objects.get(id=data["department"])
            data["department"] = department.name

        return data


class MemberRoleUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating member role and department"""

    department = serializers.PrimaryKeyRelatedField(
        queryset=Department.objects.none(), required=False, allow_null=True
    )

    class Meta:
        model = UserOrganization
        fields = ["role", "department"]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Limit department choices to the organization's departments
        if self.context.get("organization"):
            organization = self.context["organization"]
            self.fields["department"].queryset = Department.objects.filter(
                organization=organization
            )

    def validate_role(self, value):
        """Validate role changes"""
        # Prevent setting role to owner
        if value == Roles.OWNER:
            raise serializers.ValidationError(
                "Cannot assign owner role. Only one owner per organization is allowed.",
                code=ErrorCodes.CANNOT_ASSIGN_OWNER_ROLE,
            )

        return value

    def validate(self, attrs):
        """Cross-field validation"""
        role = attrs.get("role", self.instance.role if self.instance else None)
        department = attrs.get("department")

        # Employee role requires department
        if (role == Roles.EMPLOYEE or role == Roles.ADMIN) and not department:
            raise serializers.ValidationError(
                "Department is required for employee role.",
                code=ErrorCodes.DEPARTMENT_FIELD_MISSING,
            )

        # Member roles should not have department
        if role == Roles.MEMBER and department:
            attrs["department"] = None

        return attrs
