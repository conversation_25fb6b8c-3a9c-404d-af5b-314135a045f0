from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from django.contrib.auth import get_user_model
from django.db.models import Q, Count
from django.utils import timezone
from datetime import timedelta

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from jechspace_backend.utils import api_response, ErrorCodes
from users.models import UserType, UserOrganization, Roles
from permissions.constants import PermissionCategories, PermissionActions
from .mixins import OrganizationPermissionMixin, OrganizationQueryMixin
from core.pagination import CustomPageNumberPagination
from docs.schemas import (
    SUCCESS_RESPONSE_SCHEMA,
    ERROR_RESPONSE_SCHEMA,
    ORGANIZATION_SCHEMA,
    PAGINATION_SCHEMA,
    ORGANIZATION_ID_PARAMETER,
    PAGE_PARAMETER,
    PAGE_SIZE_PARAMETER,
    SEARCH_PARAMETER,
    VALIDATION_ERROR_RESPONSE,
    AUTHENTICATION_ERROR_RESPONSE,
    PERMISSION_ERROR_RESPONSE,
    NOT_FOUND_ERROR_RESPONSE
)
from docs.examples import (
    ORGANIZATION_CREATE_REQUEST,
    ORGANIZATION_SUCCESS_RESPONSE,
    ORGANIZATION_ONLY_STATS_RESPONSE
)

from .models import Organization
from .serializers import (
    OrganizationCreateSerializer,
    OrganizationUpdateSerializer,
    OrganizationListSerializer,
    OrganizationDetailSerializer,
    PublicOrganizationSerializer,
    OrganizationMemberSerializer,
    MemberRoleUpdateSerializer
)

User = get_user_model()


class OrganizationCreateView(APIView):
    """Create a new organization"""
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_summary="Create a new organization",
        operation_description="""
        Create a new organization. Only users with 'organization_owner' user type can create organizations.

        **Requirements:**
        - User must be authenticated
        - User must have 'organization_owner' user type
        - Organization name must be unique
        - Valid company email required (not common email providers)

        **Process:**
        1. Validates user permissions
        2. Creates organization with provided details
        3. Automatically assigns creator as organization owner
        4. Returns created organization details
        """,
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['name', 'email', 'phone', 'website'],
            properties={
                'name': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='Organization name',
                    example='TechCorp Solutions'
                ),
                'description': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='Organization description',
                    example='Leading technology solutions provider'
                ),
                'type': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['private', 'public'],
                    default='private',
                    description='Organization visibility type'
                ),
                'email': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format='email',
                    description='Organization contact email',
                    example='<EMAIL>'
                ),
                'phone': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='Organization phone number',
                    example='+**********'
                ),
                'website': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format='uri',
                    description='Organization website URL',
                    example='https://techcorp.com'
                ),
                'employee_size': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['1-10', '11-50', '51-200', '201-500',
                          '501-1000', '1001-5000', '5000+'],
                    description='Organization size range'
                ),
                'industry': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='Industry sector',
                    example='Technology'
                ),
            }
        ),
        responses={
            201: openapi.Response(
                description="Organization created successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'status': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, example='Organization created successfully'),
                        'data': ORGANIZATION_SCHEMA,
                    }
                ),
                examples={
                    'application/json': ORGANIZATION_SUCCESS_RESPONSE
                }
            ),
            400: VALIDATION_ERROR_RESPONSE,
            401: AUTHENTICATION_ERROR_RESPONSE,
            403: openapi.Response(
                description="Forbidden - User type not allowed",
                schema=ERROR_RESPONSE_SCHEMA,
                examples={
                    'application/json': {
                        'status': 'error',
                        'message': 'Only organization owner type users can create organizations',
                        'errors': {
                            'permission': {
                                'code': 'INSUFFICIENT_PERMISSIONS',
                                'message': 'User type does not allow organization creation'
                            }
                        }
                    }
                }
            ),
        },
        tags=['Organizations'],
    )
    def post(self, request):
        # Check if user has permission to create organizations
        if request.user.user_type != UserType.ORGANIZATION_OWNER:
            return api_response(
                message="Only organization owner type users can create organizations",
                status_code=status.HTTP_403_FORBIDDEN,
                success=False,
                errors={
                    "permission": {
                        "code": ErrorCodes.INSUFFICIENT_PERMISSIONS,
                        "message": "User type does not allow organization creation"
                    }
                }
            )

        serializer = OrganizationCreateSerializer(
            data=request.data,
            context={'request': request}
        )

        if serializer.is_valid():
            organization = serializer.save()
            response_serializer = OrganizationListSerializer(
                organization,
                context={'request': request}
            )

            return api_response(
                message="Organization created successfully",
                data=response_serializer.data,
                status_code=status.HTTP_201_CREATED
            )

        return api_response(
            message="Validation failed",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
            errors=serializer.errors
        )


class UserOrganizationsListView(APIView):
    """Get organizations where the user is a member"""
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination

    @swagger_auto_schema(
        operation_summary="List user's organizations",
        operation_description="""
        Retrieve all organizations where the authenticated user is a member.

        **Features:**
        - Returns organizations with user's role and department information
        - Supports filtering by user role within organizations
        - Paginated results for better performance
        - Includes organization details and membership information

        **Use Cases:**
        - Dashboard: Show user's organization memberships
        - Navigation: List available organizations for context switching
        - Profile: Display user's organizational affiliations
        """,
        manual_parameters=[
            openapi.Parameter(
                'role',
                openapi.IN_QUERY,
                description="Filter by user's role in organizations",
                type=openapi.TYPE_STRING,
                enum=['owner', 'admin', 'employee', 'member'],
                required=False
            ),
            PAGE_PARAMETER,
            PAGE_SIZE_PARAMETER,
        ],
        responses={
            200: openapi.Response(
                description="Organizations retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'status': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, example='Organizations retrieved successfully'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'count': openapi.Schema(type=openapi.TYPE_INTEGER, example=3),
                                'next': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                                'previous': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                                'results': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            **ORGANIZATION_SCHEMA.properties,
                                            'user_role': openapi.Schema(type=openapi.TYPE_STRING, example='admin'),
                                            'department': openapi.Schema(type=openapi.TYPE_STRING, nullable=True, example='Engineering'),
                                        }
                                    )
                                )
                            }
                        )
                    }
                ),
                examples={
                    'application/json': {
                        'status': 'success',
                        'message': 'Organizations retrieved successfully',
                        'data': {
                            'count': 2,
                            'next': None,
                            'previous': None,
                            'results': [
                                {
                                    'id': '770fa622-g4bd-63f6-c938-668877662222',
                                    'name': 'TechCorp Solutions',
                                    'slug': 'techcorp-solutions',
                                    'type': 'private',
                                    'user_role': 'owner',
                                    'department': None,
                                    'industry': 'Technology'
                                },
                                {
                                    'id': '881fb733-h5ce-74g7-d049-779988773333',
                                    'name': 'StartupHub',
                                    'slug': 'startuphub',
                                    'type': 'public',
                                    'user_role': 'member',
                                    'department': 'Engineering',
                                    'industry': 'Coworking'
                                }
                            ]
                        }
                    }
                }
            ),
            401: AUTHENTICATION_ERROR_RESPONSE,
        },
        tags=['Organizations'],
    )
    def get(self, request):
        # Get user's organizations
        user_orgs = UserOrganization.objects.filter(
            user=request.user).select_related('organization')

        # Filter by role if specified
        role_filter = request.query_params.get('role')
        if role_filter:
            user_orgs = user_orgs.filter(role=role_filter)

        # Get the organizations
        organizations = [user_org.organization for user_org in user_orgs]

        # Apply pagination
        paginator = self.pagination_class()
        paginated_orgs = paginator.paginate_queryset(organizations, request, view=self)

        serializer = OrganizationListSerializer(
            paginated_orgs,
            many=True,
            context={'request': request}
        )

        return paginator.get_paginated_response(
            serializer.data,
            message="Organizations retrieved successfully"
        )


class PublicOrganizationsListView(APIView):
    """Get public organizations with search and filtering"""
    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination

    @swagger_auto_schema(
        operation_summary="List public organizations",
        operation_description="""
        Retrieve all public organizations available for users to discover and potentially join.

        **Features:**
        - Only returns organizations with 'public' visibility
        - Full-text search across organization names and descriptions
        - Filter by industry sector
        - Paginated results with sorting options
        - Useful for organization discovery and join requests

        **Use Cases:**
        - Organization Directory: Browse available public organizations
        - Join Requests: Find organizations to request membership
        - Discovery: Explore organizations in specific industries
        """,
        manual_parameters=[
            SEARCH_PARAMETER,
            openapi.Parameter(
                'industry',
                openapi.IN_QUERY,
                description="Filter by industry sector",
                type=openapi.TYPE_STRING,
                required=False,
                example='Technology'
            ),
            PAGE_PARAMETER,
            PAGE_SIZE_PARAMETER,
        ],
        responses={
            200: openapi.Response(
                description="Public organizations retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'status': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, example='Public organizations retrieved successfully'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'count': openapi.Schema(type=openapi.TYPE_INTEGER, example=15),
                                'next': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                                'previous': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
                                'results': openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=ORGANIZATION_SCHEMA
                                )
                            }
                        )
                    }
                ),
                examples={
                    'application/json': {
                        'status': 'success',
                        'message': 'Public organizations retrieved successfully',
                        'data': {
                            'count': 2,
                            'next': None,
                            'previous': None,
                            'results': [
                                {
                                    'id': '990fc844-i6df-85h8-e15a-88aa99884444',
                                    'name': 'OpenTech Hub',
                                    'slug': 'opentech-hub',
                                    'description': 'Open source technology community',
                                    'type': 'public',
                                    'industry': 'Technology',
                                    'employee_size': '51-200',
                                    'website': 'https://opentech-hub.com'
                                }
                            ]
                        }
                    }
                }
            ),
            401: AUTHENTICATION_ERROR_RESPONSE,
        },
        tags=['Organizations'],
    )
    def get(self, request):
        # Base queryset for public organizations
        queryset = Organization.objects.filter(type='public')

        # Search functionality
        search = request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search)
            )

        # Industry filter
        industry = request.query_params.get('industry')
        if industry:
            queryset = queryset.filter(industry__iexact=industry)

        # Apply pagination
        paginator = self.pagination_class()
        paginated_orgs = paginator.paginate_queryset(queryset, request, view=self)

        serializer = PublicOrganizationSerializer(
            paginated_orgs,
            many=True,
            context={'request': request}
        )

        return paginator.get_paginated_response(
            serializer.data,
            message="Public organizations retrieved successfully"
        )


class OrganizationDetailView(APIView):
    """Get detailed information about an organization"""
    permission_classes = [IsAuthenticated]

    def get(self, request, organization_id):
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return api_response(
                message="Organization not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "organization": {
                        "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                        "message": "Organization not found"
                    }
                }
            )

        # Check if organization is public or user is a member
        if organization.type == 'private':
            is_member = organization.users.filter(user=request.user).exists()
            if not is_member:
                return api_response(
                    message="Access denied to private organization",
                    status_code=status.HTTP_403_FORBIDDEN,
                    success=False,
                    errors={
                        "permission": {
                            "code": ErrorCodes.INSUFFICIENT_PERMISSIONS,
                            "message": "User is not a member of this private organization"
                        }
                    }
                )

        serializer = OrganizationDetailSerializer(
            organization,
            context={'request': request}
        )

        return api_response(
            message="Organization details retrieved successfully",
            data=serializer.data
        )


class OrganizationUpdateView(OrganizationPermissionMixin, APIView):
    """Update organization details"""
    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.MANAGE

    def patch(self, request, organization_id):
        organization = request.organization

        serializer = OrganizationUpdateSerializer(
            organization,
            data=request.data,
            partial=True,
            context={'request': request}
        )

        if serializer.is_valid():
            updated_organization = serializer.save()
            response_serializer = OrganizationDetailSerializer(
                updated_organization,
                context={'request': request}
            )
            return api_response(
                message="Organization updated successfully",
                data=response_serializer.data
            )

        return api_response(
            message="Validation failed",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
            errors=serializer.errors
        )


class OrganizationDeleteView(APIView):
    """Delete organization (Owner only)"""
    permission_classes = [IsAuthenticated]

    def delete(self, request, organization_id):
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return api_response(
                message="Organization not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "organization": {
                        "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                        "message": "Organization not found"
                    }
                }
            )

        # Check if user is the owner
        if organization.owner != request.user:
            return api_response(
                message="Only the organization owner can delete the organization",
                status_code=status.HTTP_403_FORBIDDEN,
                success=False,
                errors={
                    "permission": {
                        "code": ErrorCodes.INSUFFICIENT_PERMISSIONS,
                        "message": "Only organization owner can delete organization"
                    }
                }
            )

        organization.delete()

        return api_response(
            message="Organization deleted successfully",
            data=None
        )


class OrganizationLeaveView(APIView):
    """Leave organization (self-leave only)"""
    permission_classes = [IsAuthenticated]

    def post(self, request, organization_id):
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return api_response(
                message="Organization not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "organization": {
                        "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                        "message": "Organization not found"
                    }
                }
            )

        # Check if user is in the organization
        try:
            user_org = UserOrganization.objects.get(
                user=request.user,
                organization=organization
            )
        except UserOrganization.DoesNotExist:
            return api_response(
                message="You are not a member of this organization",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "membership": {
                        "code": ErrorCodes.USER_NOT_IN_ORGANIZATION,
                        "message": "You are not a member of this organization"
                    }
                }
            )

        # Prevent owner from leaving
        if user_org.role == Roles.OWNER:
            return api_response(
                message="Organization owner cannot leave the organization",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "ownership": {
                        "code": ErrorCodes.CANNOT_REMOVE_ORGANIZATION_OWNER,
                        "message": "Organization owner cannot leave. Transfer ownership first."
                    }
                }
            )

        # Remove user from organization
        user_org.delete()

        return api_response(
            message="Successfully left the organization",
            data=None
        )


class OrganizationMembersListView(OrganizationPermissionMixin, APIView):
    """List organization members"""
    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.VIEW
    pagination_class = CustomPageNumberPagination

    def get(self, request, organization_id):
        organization = request.organization

        # Get organization members
        members_qs = UserOrganization.objects.filter(
            organization=organization
        ).select_related('user', 'department')

        # Filter by role if specified
        role_filter = request.query_params.get('role')
        if role_filter:
            members_qs = members_qs.filter(role=role_filter)

        # Filter by department if specified
        department_filter = request.query_params.get('department')
        if department_filter:
            members_qs = members_qs.filter(
                department__name__icontains=department_filter)

        # Apply pagination
        paginator = self.pagination_class()
        paginated_members = paginator.paginate_queryset(members_qs, request, view=self)

        serializer = OrganizationMemberSerializer(
            paginated_members,
            many=True,
            context={'request': request}
        )

        return paginator.get_paginated_response(
            serializer.data,
            message="Organization members retrieved successfully"
        )


class OrganizationMemberUpdateView(OrganizationPermissionMixin, APIView):
    """Update member role and department"""
    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.MANAGE

    def patch(self, request, organization_id, user_id):
        organization = request.organization

        # Get the target user
        try:
            target_user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return api_response(
                message="User not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "user": {
                        "code": ErrorCodes.USER_NOT_FOUND,
                        "message": "User not found"
                    }
                }
            )

        # Get target user's organization membership
        try:
            target_user_org = UserOrganization.objects.get(
                user=target_user,
                organization=organization
            )
        except UserOrganization.DoesNotExist:
            return api_response(
                message="User is not a member of this organization",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "membership": {
                        "code": ErrorCodes.USER_NOT_IN_ORGANIZATION,
                        "message": "User is not a member of this organization"
                    }
                }
            )

        # Prevent modifying owner role
        if target_user_org.role == Roles.OWNER:
            return api_response(
                message="Cannot modify the organization owner's role",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "ownership": {
                        "code": ErrorCodes.CANNOT_MODIFY_ORGANIZATION_OWNER,
                        "message": "Cannot modify the organization owner's role"
                    }
                }
            )

        serializer = MemberRoleUpdateSerializer(
            target_user_org,
            data=request.data,
            partial=True,
            context={'request': request, 'organization': organization}
        )

        if serializer.is_valid():
            updated_member = serializer.save()
            response_serializer = OrganizationMemberSerializer(
                updated_member,
                context={'request': request}
            )
            return api_response(
                message="Member updated successfully",
                data=response_serializer.data
            )

        return api_response(
            message="Validation failed",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
            errors=serializer.errors
        )


class OrganizationMemberRemoveView(OrganizationPermissionMixin, APIView):
    """Remove member from organization (admin action)"""
    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.MANAGE

    def delete(self, request, organization_id, user_id):
        organization = request.organization

        # Get the target user
        try:
            target_user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            return api_response(
                message="User not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "user": {
                        "code": ErrorCodes.USER_NOT_FOUND,
                        "message": "User not found"
                    }
                }
            )

        # Get target user's organization membership
        try:
            target_user_org = UserOrganization.objects.get(
                user=target_user,
                organization=organization
            )
        except UserOrganization.DoesNotExist:
            return api_response(
                message="User is not a member of this organization",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "membership": {
                        "code": ErrorCodes.USER_NOT_IN_ORGANIZATION,
                        "message": "User is not a member of this organization"
                    }
                }
            )

        # Prevent removing owner
        if target_user_org.role == Roles.OWNER:
            return api_response(
                message="Cannot remove the organization owner",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "ownership": {
                        "code": ErrorCodes.CANNOT_REMOVE_ORGANIZATION_OWNER,
                        "message": "Cannot remove the organization owner"
                    }
                }
            )

        # Prevent self-removal (should use leave endpoint)
        if target_user == request.user:
            return api_response(
                message="Use the leave endpoint to remove yourself from the organization",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "action": {
                        "code": ErrorCodes.INVALID_ACTION,
                        "message": "Use /leave/ endpoint for self-removal"
                    }
                }
            )

        # Remove user from organization
        target_user_org.delete()

        return api_response(
            message="Member removed successfully",
            data=None
        )


class OrganizationStatsView(OrganizationPermissionMixin, APIView):
    """Get organization-specific statistics"""
    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.VIEW

    @swagger_auto_schema(
        operation_summary="Get organization statistics",
        operation_description="""
        Retrieve comprehensive statistics for a specific organization.

        **Statistics Include:**
        - Member count and role distribution
        - Location and space utilization
        - Booking patterns and trends
        - Department breakdown
        - Activity metrics over time

        **Use Cases:**
        - Organization dashboard
        - Management reporting
        - Resource planning
        - Performance monitoring
        """,
        manual_parameters=[ORGANIZATION_ID_PARAMETER],
        responses={
            200: openapi.Response(
                description="Organization statistics retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'status': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, example='Organization statistics retrieved successfully'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'organization_id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                                'organization_name': openapi.Schema(type=openapi.TYPE_STRING),
                                'overview': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'member_stats': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'space_utilization': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'booking_trends': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'period': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
                            }
                        )
                    }
                ),
                examples={
                    'application/json': ORGANIZATION_ONLY_STATS_RESPONSE
                }
            ),
            401: AUTHENTICATION_ERROR_RESPONSE,
            403: PERMISSION_ERROR_RESPONSE,
            404: NOT_FOUND_ERROR_RESPONSE,
        },
        tags=['Organizations', 'Statistics'],
    )
    def get(self, request, organization_id):
        """Get comprehensive statistics for the organization"""
        try:
            from spaces.models import Space
            from bookings.models import Booking
            from departments.models import Department

            organization = request.organization

            # Member statistics
            members = UserOrganization.objects.filter(organization=organization)
            total_members = members.count()
            active_members = members.filter(user__is_active=True).count()
            inactive_members = total_members - active_members

            # Role distribution
            role_stats = members.values('role').annotate(count=Count('id'))
            members_by_role = {item['role']: item['count'] for item in role_stats}

            # Department distribution
            dept_stats = members.filter(department__isnull=False).values(
                'department__name').annotate(count=Count('id'))
            members_by_department = {item['department__name']
                : item['count'] for item in dept_stats}

            # Location and space statistics
            locations = organization.locations.all()
            total_locations = locations.count()

            spaces = Space.objects.filter(location__organization=organization)
            total_spaces = spaces.count()

            # Booking statistics
            bookings = Booking.objects.filter(
                space__location__organization=organization)
            total_bookings = bookings.count()
            active_bookings = bookings.filter(
                status__in=['pending', 'approved'],
                start_time__lte=timezone.now(),
                end_time__gte=timezone.now()
            ).count()

            # Recent booking trends
            now = timezone.now()
            week_ago = now - timedelta(days=7)
            last_week_ago = week_ago - timedelta(days=7)

            bookings_this_week = bookings.filter(created_at__gte=week_ago).count()
            bookings_last_week = bookings.filter(
                created_at__gte=last_week_ago,
                created_at__lt=week_ago
            ).count()

            stats_data = {
                'organization_id': str(organization.id),
                'organization_name': organization.name,
                'overview': {
                    'total_members': total_members,
                    'total_locations': total_locations,
                    'total_spaces': total_spaces,
                    'total_bookings': total_bookings,
                    'active_bookings': active_bookings,
                },
                'member_stats': {
                    'active_members': active_members,
                    'inactive_members': inactive_members,
                    'members_by_role': members_by_role,
                    'members_by_department': members_by_department,
                },
                'booking_trends': {
                    'bookings_this_week': bookings_this_week,
                    'bookings_last_week': bookings_last_week,
                },
                'period': f"{timezone.now().date()}T00:00:00Z to {timezone.now().date()}T23:59:59Z"
            }

            return api_response(
                data=stats_data,
                message="Organization statistics retrieved successfully",
                status_code=status.HTTP_200_OK
            )

        except Exception as e:
            return api_response(
                success=False,
                errors={'error': str(e)},
                message="Failed to retrieve organization statistics",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
