from django.urls import path
from .views import (
    OrganizationCreateView,
    UserOrganizationsListView,
    PublicOrganizationsListView,
    OrganizationDetailView,
    OrganizationUpdateView,
    OrganizationDeleteView,
    OrganizationLeaveView,
    OrganizationMembersListView,
    OrganizationMemberUpdateView,
    OrganizationMemberRemoveView,
    OrganizationStatsView,
)

app_name = 'organizations'

urlpatterns = [
    # Organization list operations (GET for user orgs, POST for create)
    path('', UserOrganizationsListView.as_view(), name='user-organizations'),
    path('create/', OrganizationCreateView.as_view(), name='create-organization'),

    # Public organizations
    path('public/', PublicOrganizationsListView.as_view(), name='public-organizations'),

    # Organization detail operations
    path('<uuid:organization_id>/', OrganizationDetailView.as_view(),
         name='organization-detail'),
    path('<uuid:organization_id>/update/',
         OrganizationUpdateView.as_view(), name='update-organization'),
    path('<uuid:organization_id>/delete/',
         OrganizationDeleteView.as_view(), name='delete-organization'),

    # Organization membership operations
    path('<uuid:organization_id>/leave/',
         OrganizationLeaveView.as_view(), name='leave-organization'),

    # Organization member management
    path('<uuid:organization_id>/members/',
         OrganizationMembersListView.as_view(), name='organization-members'),
    path('<uuid:organization_id>/members/<uuid:user_id>/',
         OrganizationMemberUpdateView.as_view(), name='update-member'),
    path('<uuid:organization_id>/members/<uuid:user_id>/remove/',
         OrganizationMemberRemoveView.as_view(), name='remove-member'),

    # Organization statistics
    path('<uuid:organization_id>/stats/',
         OrganizationStatsView.as_view(), name='organization-stats'),
]
