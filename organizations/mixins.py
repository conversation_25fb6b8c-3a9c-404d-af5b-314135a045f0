from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from jechspace_backend.utils import ErrorCodes, api_response
from organizations.models import Organization
from permissions.constants import PermissionActions, PermissionCategories
from permissions.permissions import HasResourcePermission


class OrganizationPermissionMixin:
    """
    Mixin that provides standard permission configuration for organization views.

    Usage:
    ```
    class MyOrganizationView(OrganizationPermissionMixin, APIView):
        permission_category = PermissionCategories.BOOKING  # Required
        permission_action = PermissionActions.VIEW  # Required

        def get(self, request, organization_id):
            # Access request.organization and request.user_org_relation
            # which are populated by HasResourcePermission
            ...
    ```
    """

    permission_classes = [IsAuthenticated, HasResourcePermission]

    @property
    def permission_code(self):
        """Return the permission category as the code for HasResourcePermission"""
        if hasattr(self, "permission_category"):
            return getattr(self, "permission_category")
        # Default to organization permission if not specified
        return PermissionCategories.ORGANIZATION


class OrganizationQueryMixin:
    """
    Mixin to handle organization validation from query parameters.
    """

    @staticmethod
    def get_organization_from_request(request):
        """
        Validate and retrieve organization from request query parameters.

        Returns:
        - tuple: (organization, user, response)
            - If validation passes: (organization_object, user_object, None)
            - If validation fails: (None, None, error_response)
        """
        # Missing organization_id in request
        if not request.query_params.get("organization_id") and not hasattr(
            request, "organization"
        ):
            return (
                None,
                None,
                api_response(
                    message="Organization ID is required",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={
                        "organization": {
                            "code": ErrorCodes.REQUIRED_FIELD_MISSING,
                            "message": "Organization ID is required",
                        }
                    },
                ),
            )

        # If organization object already attached by permission middleware, use it
        if hasattr(request, "organization"):
            return request.organization, request.user, None

        # Otherwise, look up in query params
        try:
            organization_id = request.query_params.get("organization_id")
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return (
                None,
                None,
                api_response(
                    message="Organization not found",
                    status_code=status.HTTP_404_NOT_FOUND,
                    success=False,
                    errors={
                        "organization": {
                            "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                            "message": "Organization not found",
                        }
                    },
                ),
            )

        # Check if user is a member of the organization
        if not request.user.organizations.filter(organization=organization).exists():
            return (
                None,
                None,
                api_response(
                    message="User does not belong to this organization",
                    status_code=status.HTTP_403_FORBIDDEN,
                    success=False,
                    errors={
                        "user": {
                            "code": ErrorCodes.USER_NOT_IN_ORGANIZATION,
                            "message": "User does not belong to this organization",
                        }
                    },
                ),
            )

        return organization, request.user, None
