from django.conf import settings
from django.db import models
from core.models import BaseModel


class OrganizationType:
    PRIVATE = "private"
    PUBLIC = "public"

    CHOICES = (
        (PRIVATE, "Private"),
        (PUBLIC, "Public"),
    )

EMPLOYEE_SIZE_CHOICES = [
    ('1-10', '1-10 employees'),
    ('11-50', '11-50 employees'),
    ('51-200', '51-200 employees'),
    ('201-500', '201-500 employees'),
    ('501-1000', '501-1000 employees'),
    ('1001-5000', '1001-5000 employees'),
    ('5000+', '5000+ employees'),
]

class Organization(BaseModel):
    """
    Organization model representing a company or team that uses the workspace booking system.
    """

    name = models.CharField(max_length=255)
    slug = models.SlugField(unique=True)
    description = models.TextField(blank=True, null=True)

    type = models.CharField(
        max_length=10,
        choices=OrganizationType.CHOICES,
        default=OrganizationType.PRIVATE,
    )

    # Organization's logo
    logo = models.ImageField(upload_to="organization_logos/", null=True, blank=True)

    # Contact Information
    email = models.EmailField()
    phone = models.CharField(max_length=20)
    website = models.URLField(max_length=355)

    employee_size = models.CharField(max_length=20, choices=EMPLOYEE_SIZE_CHOICES, blank=True)
    industry = models.CharField(max_length=100, blank=True, null=True)

    # Owner: the admin user who created the organization.
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name="owned_organization",
        null=True
    )

    @property
    def subdomain_url(self):
        """Get the subdomain URL for this organization"""
        return f"https://{self.slug}.{settings.DOMAIN}"

    def __str__(self):
        return self.slug
