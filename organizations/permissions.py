from rest_framework import permissions
from jechspace_backend.utils import ErrorC<PERSON>, api_response
from permissions.constants import PermissionActions


class IsOrganizationOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners of an organization to perform certain actions.
    """
    message = "Only the organization owner can perform this action"
    code = ErrorCodes.PERMISSION_DENIED

    def has_object_permission(self, request, view, obj):
        """
        Check if the user is the owner of the organization.
        """
        return obj.owner == request.user


class IsUserOrHasManagePermission(permissions.BasePermission):
    """
    Custom permission to allow users to leave an organization or users with manage permission to remove others.
    """
    message = "You don't have permission to remove this user from the organization"
    code = ErrorCodes.PERMISSION_DENIED

    def has_permission(self, request, view):
        """
        Check if user is trying to remove themselves or has manage permission.
        """
        # Allow if user is trying to remove themselves
        user_id = request.data.get('user_id')
        if not user_id or str(user_id) == str(request.user.id):
            return True

        # For removing others, require manage permission
        view.permission_action = PermissionActions.MANAGE

        # The actual permission check will be done in has_object_permission
        return True

    def has_object_permission(self, request, view, obj):
        """
        Allow if user has manage permission for this organization.
        Only called for removing other users.
        """
        # Permission for removing others is handled by the mixin
        user_id = request.data.get('user_id')
        if not user_id or str(user_id) == str(request.user.id):
            return True

        # The mixin will check if user has manage permission
        return True
