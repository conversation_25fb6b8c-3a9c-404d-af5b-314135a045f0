from django.contrib import admin

from .models import Organization


class OrganizationAdmin(admin.ModelAdmin):
    list_display = (
        "name",
        "slug",
        "owner",
        "created_at",
    )  # Customize the fields to be displayed in the admin
    search_fields = (
        "name",
        "slug",
        "owner__email",
    )  # Allow searching by name, slug, and owner
    list_filter = (
        "created_at",
        "owner",
    )  # Filters to make it easier to find organizations


admin.site.register(Organization, OrganizationAdmin)
