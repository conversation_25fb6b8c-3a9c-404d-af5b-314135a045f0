# Generated by Django 5.2 on 2025-06-03 12:56

import uuid

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Organization",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=255)),
                ("slug", models.SlugField(unique=True)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "type",
                    models.Char<PERSON>ield(
                        choices=[("private", "Private"), ("public", "Public")],
                        default="private",
                        max_length=10,
                    ),
                ),
                (
                    "logo",
                    models.ImageField(
                        blank=True, null=True, upload_to="organization_logos/"
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("phone", models.Char<PERSON>ield(max_length=20)),
                ("website", models.URLField(max_length=355)),
                (
                    "employee_size",
                    models.<PERSON>r<PERSON><PERSON>(
                        blank=True,
                        choices=[
                            ("1-10", "1-10 employees"),
                            ("11-50", "11-50 employees"),
                            ("51-200", "51-200 employees"),
                            ("201-500", "201-500 employees"),
                            ("501-1000", "501-1000 employees"),
                            ("1001-5000", "1001-5000 employees"),
                            ("5000+", "5000+ employees"),
                        ],
                        max_length=20,
                    ),
                ),
                ("industry", models.CharField(blank=True, max_length=100, null=True)),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
