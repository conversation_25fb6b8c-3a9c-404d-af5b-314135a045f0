# Developer Setup Guide

## Environment Setup

1. Clone the repository
   ```bash
   git clone <repository-url>
   cd <project-name>
   ```

2. Create and activate a virtual environment
   ```bash
   python3 -m venv venv

   # On Windows
   venv\Scripts\activate

   # On macOS/Linux
   source venv/bin/activate
   ```

3. Install dependencies
   ```bash
   pip install -r requirements.txt
   ```

4. Set up pre-commit hooks (required for all developers)
   ```bash
   pre-commit install
   ```

5. Run migrations
   ```bash
   python3 manage.py migrate
   ```

6. Start redis server
   ```bash
   sudo service redis-server start
   ```

7. Start the development server
   ```bash
   python3 manage.py runserver
   ```

8. Run Celery worker
   ```bash
   celery -A jechspace_backend worker --loglevel=info
   ```

9. Run Celery Beat
   ```bash
   celery -A jechspace_backend beat --loglevel=info
   ```

## Pre-commit Hooks

We use pre-commit hooks to ensure code quality and consistency across the team. These hooks run automatically when you commit code and will prevent commits that don't meet our quality standards.

### Installed Hooks

- **Code Formatting**
  - `black`: Automatically formats python3 code
  - `isort`: Sorts and organizes imports

- **Security**
  - `bandit`: Identifies common security issues

- **General**
  - `trailing-whitespace`: Removes trailing whitespace
  - `end-of-file-fixer`: Ensures files end with a newline
  - `check-yaml` & `check-json`: Validates configuration files
  - `debug-statements`: Catches debug statements

### Working with Pre-commit

1. When you try to commit code that doesn't pass the hooks, the commit will be blocked
2. The hooks will attempt to fix issues automatically where possible
3. When auto-fixes occur, you'll need to stage the changes and try committing again
4. For errors that can't be fixed automatically, you'll need to fix them manually

To run the hooks manually (recommended before committing):
```bash
pre-commit run --all-files
```

To temporarily bypass hooks (emergency only):
```bash
git commit -m "Your message" --no-verify
```

## Permissions Management

The application uses a role-based permission system. Here's how to manage permissions:

### Setup Default Permissions
First, set up the default permissions for your application:
```bash
python3 manage.py setup_default_permissions
```

This will create the following default permissions:
- Organization permissions (view, create, update, delete, manage)
- Department permissions (view, create, update, delete, manage)
- User permissions (view, create, update, delete, manage)
- Content permissions (view, create, update, delete, manage)
- Settings permissions (view, create, update, delete, manage)

### List All Permissions
```bash
python3 manage.py manage_permissions list
```

### Add New Permission
```bash
python3 manage.py manage_permissions add \
    --permission custom_feature \
    --name "Custom Feature Management" \
    --description "Manage custom feature settings" \
    --category custom
```

### Assign Permission to Department
```bash
python3 manage.py manage_permissions assign \
    --permission organization \
    --department "department-uuid-here" \
    --actions "view,create,update,manage"
```

### Remove Permission
```bash
python3 manage.py manage_permissions remove --permission custom_feature
```

### Default Permission Categories
- Organization Management
- Department Management
- User Management
- Content Management
- Settings Management

### Permission Actions
Each permission can have these action levels:
- `view`: View resources
- `create`: Create new resources
- `update`: Modify existing resources
- `delete`: Delete resources
- `manage`: Administrative actions

## Development Workflow
### Check out [the handbook](https://github.com/JechSpace/Handbook/blob/main/1.%20GitGithubWorkflow.md)
