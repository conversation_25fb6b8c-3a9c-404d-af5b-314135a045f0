#!/bin/bash

# Function to handle shutdown
shutdown() {
  echo "Shutting down gracefully..."
  # Kill all background processes
  kill $(jobs -p) 2>/dev/null
  exit 0
}

# Set up signal handling
trap shutdown SIGTERM SIGINT

# Function to check if a process is still running
check_process() {
  kill -0 $1 2>/dev/null
  return $?
}

# Function to monitor background processes
monitor_processes() {
  while true; do
    if ! check_process $WORKER_PID; then
      echo "Celery worker died, restarting..."
      celery -A jechspace_backend worker -l info &
      WORKER_PID=$!
    fi
    
    if ! check_process $BEAT_PID; then
      echo "Celery beat died, restarting..."
      celery -A jechspace_backend beat -l info &
      BEAT_PID=$!
    fi
    
    sleep 10
  done
}

# Wait for database to be ready
echo "Waiting for database..."
while ! nc -z $DB_HOST $DB_PORT; do
  sleep 0.1
done
echo "Database is ready!"

# Run migrations
echo "Running migrations..."
python manage.py migrate

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Setup default permissions
echo "Setting up default permissions..."
python manage.py setup_default_permissions

# Start Celery worker in the background
echo "Starting Celery worker..."
celery -A jechspace_backend worker -l info &
WORKER_PID=$!

# Start Celery beat in the background
echo "Starting Celery beat..."
celery -A jechspace_backend beat -l info &
BEAT_PID=$!

# Start process monitor in the background
echo "Starting process monitor..."
monitor_processes &
MONITOR_PID=$!

# Wait for Celery processes to be ready
echo "Waiting for Celery processes to be ready..."
sleep 5

# Start Gunicorn in the foreground (this will be the main process)
echo "Starting Gunicorn..."
exec gunicorn jechspace_backend.wsgi:application --bind 0.0.0.0:7632 --workers 4 --threads 2 --timeout 120 
