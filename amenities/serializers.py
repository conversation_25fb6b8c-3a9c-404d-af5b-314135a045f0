from rest_framework import serializers

from .models import Amenity


class AmenitySerializer(serializers.ModelSerializer):
    """Serializer for amenities."""

    class Meta:
        model = Amenity
        fields = [
            "id",
            "name",
            "slug",
            "description",
            "icon",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def create(self, validated_data):
        user = self.context["request"].user
        validated_data["created_by"] = user
        return super().create(validated_data)
