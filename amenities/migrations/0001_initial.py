# Generated by Django 5.2 on 2025-06-03 12:56

import uuid

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Amenity",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=255)),
                ("slug", models.SlugField(max_length=255, unique=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("icon", models.ImageField(upload_to="amenities/icons/")),
            ],
            options={
                "verbose_name": "Amenity",
                "verbose_name_plural": "Amenities",
                "ordering": ["name"],
            },
        ),
    ]
