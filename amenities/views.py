from django.db.models import Q

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status, viewsets
from rest_framework.parsers import <PERSON>Parser, MultiPartParser

from core.pagination import CustomPageNumberPagination
from jechspace_backend.utils import api_response
from organizations.mixins import OrganizationPermissionMixin
from permissions.constants import PermissionActions, PermissionCategories
from permissions.permissions import IsSuperuser

from .models import Amenity
from .serializers import AmenitySerializer


class AmenityViewSet(OrganizationPermissionMixin, viewsets.ModelViewSet):
    """ViewSet for managing amenities."""

    permission_category = PermissionCategories.AMENITIES
    permission_action = PermissionActions.MANAGE

    serializer_class = AmenitySerializer
    pagination_class = CustomPageNumberPagination
    parser_classes = [MultiPartParser, FormParser]
    lookup_field = "id"
    http_method_names = ["get", "post", "patch", "delete", "head", "options"]

    # Swagger documentation
    list_amenities_params = [
        openapi.Parameter(
            "search",
            openapi.IN_QUERY,
            description="Search term",
            type=openapi.TYPE_STRING,
        ),
        openapi.Parameter(
            "page",
            openapi.IN_QUERY,
            description="Page number",
            type=openapi.TYPE_INTEGER,
        ),
        openapi.Parameter(
            "limit",
            openapi.IN_QUERY,
            description="Results per page",
            type=openapi.TYPE_INTEGER,
        ),
    ]

    def get_permissions(self):
        """
        Override to use IsSuperuser permission for update and partial_update methods.
        """
        if self.action in ["update", "partial_update", "create", "delete"]:
            return [IsSuperuser()]
        return super().get_permissions()

    def get_queryset(self):
        """Get the list of amenities with optional filtering."""
        queryset = Amenity.objects.all()
        search = self.request.query_params.get("search", None)

        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(description__icontains=search)
            )

        return queryset

    @swagger_auto_schema(
        operation_description="List all amenities",
        manual_parameters=list_amenities_params,
        operation_summary="List Amenities",
        responses={
            200: openapi.Response(
                description="Success",
                examples={
                    "application/json": {
                        "status": "success",
                        "message": "Amenities retrieved successfully",
                        "data": {
                            "amenities": [
                                {
                                    "id": "550e8400-e29b-41d4-a716-446655440060",
                                    "name": "High-speed WiFi",
                                    "description": "Enterprise-grade WiFi connection (1Gbps)",
                                    "slug": "wifi",
                                    "icon": "[Icon Upload]",
                                    "created_at": "2025-04-30T10:30:00Z",
                                }
                            ],
                            "pagination": {
                                "total": 10,
                                "pages": 1,
                                "page": 1,
                                "limit": 10,
                            },
                        },
                    }
                },
            )
        },
    )
    def list(self, request, *args, **kwargs):
        """
        List all amenities with pagination and optional filtering.
        """
        self.permission_action = PermissionActions.VIEW
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginator.paginate_queryset(queryset, request, self)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.paginator.get_paginated_response(
                serializer.data, message="Amenities retrieved successfully"
            )

        serializer = self.get_serializer(queryset, many=True)
        return api_response(
            message="Amenities retrieved successfully",
            data={"items": serializer.data, "pagination": None},
        )

    @swagger_auto_schema(
        operation_description="Create a new amenity (Admin only)",
        operation_summary="Create Amenity",
        request_body=AmenitySerializer,
        responses={
            201: openapi.Response(
                description="Created",
                examples={
                    "application/json": {
                        "status": "success",
                        "message": "Amenity created successfully",
                        "data": {
                            "id": "550e8400-e29b-41d4-a716-446655440060",
                            "name": "High-speed WiFi",
                            "slug": "wifi",
                            "description": "Enterprise-grade WiFi connection (1Gbps)",
                            "icon": "[Icon Upload]",
                            "created_at": "2025-04-30T10:30:00Z",
                        },
                    }
                },
            )
        },
    )
    def create(self, request, *args, **kwargs):
        """Create a new amenity (Admin only)."""
        self.permission_action = PermissionActions.CREATE

        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)

        return api_response(
            message="Amenity created successfully",
            data=serializer.data,
            status_code=status.HTTP_201_CREATED,
        )

    @swagger_auto_schema(
        operation_description="Retrieve a specific amenity by ID (Admin only)",
        operation_summary="Get Amenity Detail",
        responses={
            200: openapi.Response(
                description="Success",
                examples={
                    "application/json": {
                        "status": "success",
                        "message": "Amenity retrieved successfully",
                        "data": {
                            "id": "550e8400-e29b-41d4-a716-446655440060",
                            "name": "Ultra-high-speed WiFi",
                            "description": "Enterprise-grade WiFi connection (10Gbps)",
                            "slug": "wifi",
                            "icon": "[Icon Upload]",
                            "created_at": "2025-04-30T11:15:00Z",
                            "updated_at": "2025-04-30T11:15:00Z",
                        },
                    }
                },
            )
        },
    )
    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific amenity by ID."""
        self.permission_action = PermissionActions.VIEW
        instance = self.get_object()
        serializer = self.get_serializer(instance)

        return api_response(
            message="Amenity retrieved successfully", data=serializer.data
        )

    @swagger_auto_schema(
        operation_description="Update an amenity (Platform Admin only)",
        operation_summary="Update Amenity",
        request_body=AmenitySerializer(partial=True),
        responses={
            200: openapi.Response(
                description="Success",
                examples={
                    "application/json": {
                        "status": "success",
                        "message": "Amenity updated successfully",
                        "data": {
                            "id": "550e8400-e29b-41d4-a716-446655440060",
                            "name": "Ultra-high-speed WiFi",
                            "description": "Enterprise-grade WiFi connection (10Gbps)",
                            "slug": "wifi",
                            "icon": "[Icon Upload]",
                            "updated_at": "2025-04-30T11:15:00Z",
                        },
                    }
                },
            )
        },
    )
    def partial_update(self, request, *args, **kwargs):
        """Update an amenity (Platform Admin only) - PATCH method."""
        kwargs["partial"] = True
        return self.update(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        """Update an amenity (Admin only)."""
        partial = kwargs.pop("partial", False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return api_response(
            message="Amenity updated successfully", data=serializer.data
        )

    @swagger_auto_schema(
        operation_description="Delete an amenity (Platform Admin only)",
        operation_summary="Delete Amenity",
        responses={
            200: openapi.Response(
                description="Success",
                examples={
                    "application/json": {
                        "status": "success",
                        "message": "Amenity deleted successfully",
                        "data": None,
                    }
                },
            )
        },
    )
    def destroy(self, request, *args, **kwargs):
        """Delete an amenity (Admin only)."""
        instance = self.get_object()
        self.perform_destroy(instance)

        return api_response(message="Amenity deleted successfully", data=None)
