from django.contrib.auth import get_user_model
from django.db import models
from django.utils.text import slugify

from core.models import BaseModel

User = get_user_model()


class Amenity(BaseModel):
    """Model for amenities available at locations."""

    name = models.CharField(max_length=255)
    slug = models.SlugField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    icon = models.ImageField(upload_to="amenities/icons/")

    class Meta:
        verbose_name = "Amenity"
        verbose_name_plural = "Amenities"
        ordering = ["name"]

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        super().save(*args, **kwargs)
