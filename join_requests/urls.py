from django.urls import path

from .views import (
    JoinRequestCreateView,
    JoinRequestDetailView,
    OrganizationJoinRequestsView,
    UserJoinRequestDetailView,
    UserJoinRequestsView,
    BulkJoinRequestUpdateView,
    JoinRequestStatsView,
    JoinRequestFilterView
)

app_name = "join_requests"

urlpatterns = [
    # Organization join request endpoints
    path(
        "organizations/<uuid:organization_id>/join-requests/",
        JoinRequestCreateView.as_view(),
        name="create-join-request",
    ),
    path(
        "organizations/<uuid:organization_id>/join-requests/list/",
        OrganizationJoinRequestsView.as_view(),
        name="list-organization-join-requests",
    ),
    path(
        "organizations/<uuid:organization_id>/join-requests/filter/",
        JoinRequestFilterView.as_view(),
        name="filter-join-requests",
    ),
    path(
        "organizations/<uuid:organization_id>/join-requests/bulk/",
        BulkJoinRequestUpdateView.as_view(),
        name="bulk-update-join-requests",
    ),
    path(
        "organizations/<uuid:organization_id>/join-requests/stats/",
        JoinRequestStatsView.as_view(),
        name="join-request-stats",
    ),
    path(
        "organizations/<uuid:organization_id>/join-requests/<uuid:request_id>/",
        JoinRequestDetailView.as_view(),
        name="join-request-detail",
    ),
    # User join request endpoints
    path(
        "users/join-requests/",
        UserJoinRequestsView.as_view(),
        name="list-user-join-requests",
    ),
    path(
        "users/join-requests/<uuid:request_id>/",
        UserJoinRequestDetailView.as_view(),
        name="user-join-request-detail",
    ),
]
