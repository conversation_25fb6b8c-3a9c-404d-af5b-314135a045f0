from datetime import datetime
from django.db.models import Q
from django.utils import timezone
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from core.pagination import CustomPageNumberPagination
from jechspace_backend.utils import ErrorCodes, api_response
from organizations.mixins import OrganizationPermissionMixin
from permissions.constants import PermissionActions, PermissionCategories
from users.models import Roles, UserOrganization

from .models import JoinRequest
from .serializers import (
    JoinRequestSerializer,
    JoinRequestUpdateSerializer,
    JoinRequestDetailSerializer,
    BulkJoinRequestUpdateSerializer,
    JoinRequestCancelSerializer
)


class JoinRequestCreateView(APIView):
    """
    API view for creating join requests for public organizations
    """

    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination

    @swagger_auto_schema(
        operation_summary="Create join request",
        operation_description="Creates a join request for a public organization.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["message"],
            properties={
                "message": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    example="I would like to join this organization because of my interest in workspace management.",
                ),
            },
        ),
        responses={
            201: openapi.Response(
                description="Join request submitted successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Join request submitted successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440030",
                                ),
                                "organization": openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    properties={
                                        "id": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="550e8400-e29b-41d4-a716-446655440010",
                                        ),
                                        "name": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="Workspace Solutions",
                                        ),
                                    },
                                ),
                                "status": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="pending"
                                ),
                                "created_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2025-04-30T13:15:00Z",
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Invalid data or organization is not public"
            ),
            404: openapi.Response(description="Organization not found"),
        },
        tags=["Join Requests"],
    )
    def post(self, request, organization_id):
        """
        Create a join request for a public organization
        """
        data = {
            "organization_id": organization_id,
            "message": request.data.get("message", ""),
        }

        serializer = JoinRequestSerializer(data=data, context={"request": request})

        if serializer.is_valid():
            serializer.save()

            return api_response(
                data=serializer.data,
                message="Join request submitted successfully",
                status_code=status.HTTP_201_CREATED,
            )

        return api_response(
            errors=serializer.errors,
            message="Validation error",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
        )


class OrganizationJoinRequestsView(OrganizationPermissionMixin, APIView):
    """
    API view for listing join requests for an organization (admin only)
    """

    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.MANAGE
    pagination_class = CustomPageNumberPagination
    ordering_fields = ['created_at', 'updated_at', 'status']
    ordering = ['-created_at']

    @swagger_auto_schema(
        operation_summary="List organization join requests",
        operation_description="Lists all join requests for the specified organization. Admin only.",
        manual_parameters=[
            openapi.Parameter(
                "page",
                openapi.IN_QUERY,
                description="Page number",
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                "limit",
                openapi.IN_QUERY,
                description="Number of items per page",
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                "status",
                openapi.IN_QUERY,
                description="Filter by status",
                type=openapi.TYPE_STRING,
                enum=["pending", "approved", "rejected"],
            ),
            openapi.Parameter(
                "search",
                openapi.IN_QUERY,
                description="Search by username, email, first name, or last name",
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                "ordering",
                openapi.IN_QUERY,
                description="Order by field (prefix with - for descending order)",
                type=openapi.TYPE_STRING,
                enum=["created_at", "-created_at", "updated_at", "-updated_at", "status", "-status"],
            ),
            openapi.Parameter(
                "from_date",
                openapi.IN_QUERY,
                description="Filter by date from (format: YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                format="date"
            ),
            openapi.Parameter(
                "to_date",
                openapi.IN_QUERY,
                description="Filter by date to (format: YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                format="date"
            ),
        ],
        responses={
            200: openapi.Response(description="Join requests retrieved successfully"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Organization not found"),
        },
        tags=["Join Requests"],
    )
    def get(self, request, organization_id):
        """
        List all join requests for an organization (admin only)
        """
        # Get query parameters
        status_filter = request.query_params.get("status", JoinRequest.JoinRequestStatus.PENDING)
        search_query = request.query_params.get("search", "")
        from_date = request.query_params.get("from_date")
        to_date = request.query_params.get("to_date")
        ordering = request.query_params.get("ordering", "-created_at")
        
        # Validate ordering
        if ordering.lstrip('-') not in self.ordering_fields:
            ordering = self.ordering[0]

        # Build base queryset
        join_requests = JoinRequest.objects.filter(organization_id=organization_id)
        
        # Apply status filter
        if status_filter:
            join_requests = join_requests.filter(status=status_filter)
            
        # Apply search filter
        if search_query:
            join_requests = join_requests.filter(
                Q(user__email__icontains=search_query) |
                Q(user__first_name__icontains=search_query) |
                Q(user__last_name__icontains=search_query) |
                Q(user__username__icontains=search_query)
            )
            
        # Apply date filters
        if from_date:
            try:
                from_datetime = datetime.strptime(from_date, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                join_requests = join_requests.filter(created_at__gte=from_datetime)
            except ValueError:
                pass
                
        if to_date:
            try:
                to_datetime = datetime.strptime(to_date, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                to_datetime = to_datetime.replace(hour=23, minute=59, second=59)
                join_requests = join_requests.filter(created_at__lte=to_datetime)
            except ValueError:
                pass
        
        # Apply ordering
        join_requests = join_requests.order_by(ordering)

        # Apply pagination
        paginator = self.pagination_class()
        paginated_requests = paginator.paginate_queryset(join_requests, request, self)

        # Serialize the data
        serializer = JoinRequestSerializer(paginated_requests, many=True, context={'request': request})

        return paginator.get_paginated_response(
            serializer.data, message="Join requests retrieved successfully"
        )


class JoinRequestDetailView(OrganizationPermissionMixin, APIView):
    """
    API view for managing join requests (view details, approve, or reject)
    """

    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.UPDATE

    @swagger_auto_schema(
        operation_summary="Get join request details",
        operation_description="Get detailed information about a specific join request",
        responses={
            200: openapi.Response(description="Join request details retrieved successfully"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Join request not found"),
        },
        tags=["Join Requests"],
    )
    def get(self, request, organization_id, request_id):
        """
        Get detailed information about a join request
        """
        try:
            join_request = JoinRequest.objects.get(
                id=request_id, organization_id=organization_id
            )
            
            serializer = JoinRequestDetailSerializer(join_request)
            return api_response(
                data=serializer.data,
                message="Join request details retrieved successfully",
                status_code=status.HTTP_200_OK,
            )
        except JoinRequest.DoesNotExist:
            return api_response(
                errors={
                    "join_request": {
                        "code": ErrorCodes.JOIN_REQUEST_NOT_FOUND,
                        "message": "Join request not found",
                    }
                },
                message="Join request not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )

    @swagger_auto_schema(
        operation_summary="Approve or reject join request",
        operation_description="Approves or rejects a join request for the organization. Admin only.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["status"],
            properties={
                "status": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["approved", "rejected"],
                    example="approved",
                ),
                "role": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["member", "employee", "admin"],
                    example="member",
                ),
                "department": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    example="550e8400-e29b-41d4-a716-446655440000",
                ),
                "reason": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    example="Currently not accepting new members in the Design department",
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Join request processed successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Join request approved successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "user": openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    properties={
                                        "id": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="550e8400-e29b-41d4-a716-446655440002",
                                        ),
                                        "email": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="<EMAIL>",
                                        ),
                                        "first_name": openapi.Schema(
                                            type=openapi.TYPE_STRING, example="Alex"
                                        ),
                                        "last_name": openapi.Schema(
                                            type=openapi.TYPE_STRING, example="Johnson"
                                        ),
                                    },
                                ),
                                "role": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="member"
                                ),
                                "department": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="Design"
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Invalid data"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Join request not found"),
        },
        tags=["Join Requests"],
    )
    def patch(self, request, organization_id, request_id):
        """
        Approve or reject a join request
        """
        # Get the join request
        try:
            join_request = JoinRequest.objects.get(
                id=request_id, organization_id=organization_id
            )
        except JoinRequest.DoesNotExist:
            return api_response(
                errors={
                    "join_request": {
                        "code": ErrorCodes.JOIN_REQUEST_NOT_FOUND,
                        "message": "Join request not found",
                    }
                },
                message="Join request not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )

        # Check if the join request is already processed
        if join_request.status != JoinRequest.JoinRequestStatus.PENDING:
            return api_response(
                errors={
                    "join_request": {
                        "code": ErrorCodes.JOIN_REQUEST_ALREADY_ACCEPTED,
                        "message": f"Join request is already {join_request.status}",
                    }
                },
                message=f"Join request already {join_request.status}",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
            )

        # Validate and update the join request
        serializer = JoinRequestUpdateSerializer(
            join_request, data=request.data, partial=True, context={"request": request}
        )

        if serializer.is_valid():
            serializer.save()

            return api_response(
                data=serializer.data,
                message=f"Join request {join_request.status} successfully",
                status_code=status.HTTP_200_OK,
            )

        return api_response(
            errors=serializer.errors,
            message="Validation error",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
        )


class UserJoinRequestsView(APIView):
    """
    API view for listing and canceling user's join requests
    """

    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination

    @swagger_auto_schema(
        operation_summary="List user's join requests",
        operation_description="Lists all join requests created by the current user.",
        manual_parameters=[
            openapi.Parameter(
                "page",
                openapi.IN_QUERY,
                description="Page number",
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                "limit",
                openapi.IN_QUERY,
                description="Number of items per page",
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                "status",
                openapi.IN_QUERY,
                description="Filter by status",
                type=openapi.TYPE_STRING,
                enum=["pending", "approved", "rejected"],
            ),
        ],
        responses={
            200: openapi.Response(
                description="User join requests retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="User join requests retrieved successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "items": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            "id": openapi.Schema(
                                                type=openapi.TYPE_STRING,
                                                example="550e8400-e29b-41d4-a716-446655440031",
                                            ),
                                            "organization": openapi.Schema(
                                                type=openapi.TYPE_OBJECT,
                                                properties={
                                                    "id": openapi.Schema(
                                                        type=openapi.TYPE_STRING,
                                                        example="550e8400-e29b-41d4-a716-446655440011",
                                                    ),
                                                    "name": openapi.Schema(
                                                        type=openapi.TYPE_STRING,
                                                        example="Tech Hub Coworking",
                                                    ),
                                                    "logo": openapi.Schema(
                                                        type=openapi.TYPE_STRING,
                                                        example="https://example.com/media/logos/tech-hub.jpg",
                                                    ),
                                                },
                                            ),
                                            "message": openapi.Schema(
                                                type=openapi.TYPE_STRING,
                                                example="I'm interested in joining your tech community.",
                                            ),
                                            "status": openapi.Schema(
                                                type=openapi.TYPE_STRING,
                                                example="pending",
                                            ),
                                            "created_at": openapi.Schema(
                                                type=openapi.TYPE_STRING,
                                                format="date-time",
                                                example="2025-04-28T16:45:00Z",
                                            ),
                                        },
                                    ),
                                ),
                                "pagination": openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    properties={
                                        "total": openapi.Schema(
                                            type=openapi.TYPE_INTEGER, example=1
                                        ),
                                        "pages": openapi.Schema(
                                            type=openapi.TYPE_INTEGER, example=1
                                        ),
                                        "page": openapi.Schema(
                                            type=openapi.TYPE_INTEGER, example=1
                                        ),
                                        "limit": openapi.Schema(
                                            type=openapi.TYPE_INTEGER, example=10
                                        ),
                                    },
                                ),
                            },
                        ),
                    },
                ),
            ),
        },
        tags=["Join Requests"],
    )
    def get(self, request):
        """
        List all join requests created by the current user
        """
        # Get query parameters
        status_filter = request.query_params.get("status")

        # Get user's join requests
        join_requests = JoinRequest.objects.filter(user=request.user)

        # Apply status filter if provided
        if status_filter:
            join_requests = join_requests.filter(status=status_filter)

        # Apply pagination
        paginator = self.pagination_class()
        paginated_requests = paginator.paginate_queryset(join_requests, request)

        # Serialize the data
        serializer = JoinRequestSerializer(paginated_requests, many=True)

        return paginator.get_paginated_response(
            serializer.data, message="User join requests retrieved successfully"
        )


class JoinRequestStatsView(OrganizationPermissionMixin, APIView):
    """
    API view for getting join request statistics for an organization
    """
    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.VIEW

    @swagger_auto_schema(
        operation_summary="Get join request statistics",
        operation_description="Get statistics about join requests for the organization",
        responses={
            200: openapi.Response(description="Statistics retrieved successfully"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Organization not found"),
        },
        tags=["Join Requests"],
    )
    def get(self, request, organization_id):
        """
        Get statistics about join requests for the organization
        """
        # Ensure the organization exists
        try:
            organization = Organization.objects.get(id=organization_id)
        except Organization.DoesNotExist:
            return api_response(
                errors={
                    "organization": {
                        "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                        "message": "Organization not found",
                    }
                },
                message="Organization not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )
            
        # Get all join requests for the organization
        join_requests = JoinRequest.objects.filter(organization=organization)
        
        # Calculate statistics
        total_requests = join_requests.count()
        pending_requests = join_requests.filter(status=JoinRequest.JoinRequestStatus.PENDING).count()
        approved_requests = join_requests.filter(status=JoinRequest.JoinRequestStatus.APPROVED).count()
        rejected_requests = join_requests.filter(status=JoinRequest.JoinRequestStatus.REJECTED).count()
        
        # Calculate approval rate
        processed_requests = approved_requests + rejected_requests
        approval_rate = (approved_requests / processed_requests * 100) if processed_requests > 0 else 0
        
        # Get recent trend (last 7 days)
        from django.utils import timezone
        from datetime import timedelta
        
        seven_days_ago = timezone.now() - timedelta(days=7)
        recent_requests = join_requests.filter(created_at__gte=seven_days_ago).count()
        
        stats = {
            "total_requests": total_requests,
            "pending_requests": pending_requests,
            "approved_requests": approved_requests,
            "rejected_requests": rejected_requests,
            "approval_rate": round(approval_rate, 2),
            "recent_requests": recent_requests
        }
        
        return api_response(
            data=stats,
            message="Join request statistics retrieved successfully",
            status_code=status.HTTP_200_OK,
        )


class UserJoinRequestDetailView(APIView):
    """
    API view for managing a user's join request
    """

    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_summary="Get join request details",
        operation_description="Get details of a specific join request created by the current user",
        responses={
            200: openapi.Response(description="Join request details retrieved successfully"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Join request not found"),
        },
        tags=["Join Requests"],
    )
    def get(self, request, request_id):
        """
        Get details of a specific join request
        """
        try:
            join_request = JoinRequest.objects.get(id=request_id, user=request.user)
            serializer = JoinRequestDetailSerializer(join_request)
            return api_response(
                data=serializer.data,
                message="Join request details retrieved successfully",
                status_code=status.HTTP_200_OK,
            )
        except JoinRequest.DoesNotExist:
            return api_response(
                errors={
                    "join_request": {
                        "code": ErrorCodes.JOIN_REQUEST_NOT_FOUND,
                        "message": "Join request not found",
                    }
                },
                message="Join request not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )

    @swagger_auto_schema(
        operation_summary="Cancel join request",
        operation_description="Cancels a pending join request created by the current user with an optional reason.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "cancellation_reason": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    example="Found another organization that better suits my needs."
                ),
            },
        ),
        responses={
            200: openapi.Response(description="Join request cancelled successfully"),
            400: openapi.Response(description="Join request is not pending"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Join request not found"),
        },
        tags=["Join Requests"],
    )
    def delete(self, request, request_id):
        """
        Cancel a pending join request with optional reason
        """
        # Get the join request
        try:
            join_request = JoinRequest.objects.get(id=request_id, user=request.user)
        except JoinRequest.DoesNotExist:
            return api_response(
                errors={
                    "join_request": {
                        "code": ErrorCodes.JOIN_REQUEST_NOT_FOUND,
                        "message": "Join request not found",
                    }
                },
                message="Join request not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )

        # Check if the join request is pending
        if join_request.status != JoinRequest.JoinRequestStatus.PENDING:
            return api_response(
                errors={
                    "join_request": {
                        "code": ErrorCodes.INVALID_FORMAT,
                        "message": f"Cannot cancel a {join_request.status} join request",
                    }
                },
                message=f"Join request is {join_request.status}",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
            )

        # Process cancellation reason if provided
        serializer = JoinRequestCancelSerializer(data=request.data)
        if serializer.is_valid():
            # Store the reason in a log or audit trail if needed
            cancellation_reason = serializer.validated_data.get("cancellation_reason", "")
            
            # For demo purposes, we'll just print it
            if cancellation_reason:
                print(f"Join request {join_request.id} cancelled with reason: {cancellation_reason}")

        # Delete the join request
        join_request.delete()

        return api_response(
            message="Join request cancelled successfully",
            status_code=status.HTTP_200_OK,
        )


class BulkJoinRequestUpdateView(OrganizationPermissionMixin, APIView):
    """
    API view for processing multiple join requests at once
    """
    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.UPDATE

    @swagger_auto_schema(
        operation_summary="Bulk update join requests",
        operation_description="Approve or reject multiple join requests at once",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["join_request_ids", "status"],
            properties={
                "join_request_ids": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    example=["550e8400-e29b-41d4-a716-446655440030", "550e8400-e29b-41d4-a716-446655440031"]
                ),
                "status": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["approved", "rejected"],
                    example="approved"
                ),
                "role": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=["member", "employee", "admin"],
                    example="member"
                ),
                "department": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    example="550e8400-e29b-41d4-a716-446655440000"
                ),
            }
        ),
        responses={
            200: openapi.Response(description="Join requests processed successfully"),
            400: openapi.Response(description="Invalid data"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Organization not found"),
        },
        tags=["Join Requests"],
    )
    def post(self, request, organization_id):
        """
        Process multiple join requests at once
        """
        serializer = BulkJoinRequestUpdateSerializer(
            data=request.data, 
            context={"request": request, "organization_id": organization_id}
        )
        
        if not serializer.is_valid():
            return api_response(
                errors=serializer.errors,
                message="Validation error",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
            )
            
        # Get validated data
        join_request_ids = serializer.validated_data["join_request_ids"]
        new_status = serializer.validated_data["status"]
        role = serializer.validated_data.get("role", Roles.MEMBER)
        department = serializer.validated_data.get("department")
        
        # Get the join requests
        join_requests = JoinRequest.objects.filter(
            id__in=join_request_ids,
            organization_id=organization_id,
            status=JoinRequest.JoinRequestStatus.PENDING
        )
        
        # Process join requests
        processed_count = 0
        for join_request in join_requests:
            # Update request status
            join_request.status = new_status
            join_request.reviewed_by = request.user
            join_request.save()
            
            # If approved, create user-organization relationship
            if new_status == JoinRequest.JoinRequestStatus.APPROVED:
                UserOrganization.objects.create(
                    user=join_request.user,
                    organization=join_request.organization,
                    role=role,
                    department=department,
                )
            
            processed_count += 1
            
        action = "approved" if new_status == JoinRequest.JoinRequestStatus.APPROVED else "rejected"
        return api_response(
            data={
                "processed_count": processed_count,
                "status": new_status
            },
            message=f"{processed_count} join requests {action} successfully",
            status_code=status.HTTP_200_OK,
        )


class JoinRequestFilterView(OrganizationPermissionMixin, APIView):
    """
    API view for advanced filtering and search of join requests
    """
    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.VIEW
    pagination_class = CustomPageNumberPagination

    @swagger_auto_schema(
        operation_summary="Filter join requests",
        operation_description="Advanced filtering and search for join requests",
        manual_parameters=[
            openapi.Parameter(
                "page",
                openapi.IN_QUERY,
                description="Page number",
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                "limit",
                openapi.IN_QUERY,
                description="Number of items per page",
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                "status",
                openapi.IN_QUERY,
                description="Filter by status",
                type=openapi.TYPE_STRING,
                enum=["pending", "approved", "rejected"],
            ),
            openapi.Parameter(
                "search",
                openapi.IN_QUERY,
                description="Search by username, email, first name, or last name",
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                "order_by",
                openapi.IN_QUERY,
                description="Order by field",
                type=openapi.TYPE_STRING,
                enum=["created_at", "updated_at", "status"],
            ),
            openapi.Parameter(
                "order_direction",
                openapi.IN_QUERY,
                description="Order direction",
                type=openapi.TYPE_STRING,
                enum=["asc", "desc"],
            ),
            openapi.Parameter(
                "from_date",
                openapi.IN_QUERY,
                description="Filter by date from (format: YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
            ),
            openapi.Parameter(
                "to_date",
                openapi.IN_QUERY,
                description="Filter by date to (format: YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
            ),
        ],
        responses={
            200: openapi.Response(description="Join requests retrieved successfully"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Organization not found"),
        },
        tags=["Join Requests"],
    )
    def get(self, request, organization_id):
        """
        Advanced filtering and search for join requests
        """
        # Get query parameters
        status_filter = request.query_params.get("status")
        search_query = request.query_params.get("search", "")
        order_by = request.query_params.get("order_by", "created_at")
        order_direction = request.query_params.get("order_direction", "desc")
        from_date = request.query_params.get("from_date")
        to_date = request.query_params.get("to_date")
        
        # Get join requests
        join_requests = JoinRequest.objects.filter(organization_id=organization_id)
        
        # Apply status filter
        if status_filter:
            join_requests = join_requests.filter(status=status_filter)
            
        # Apply search filter
        if search_query:
            join_requests = join_requests.filter(
                Q(user__email__icontains=search_query) |
                Q(user__first_name__icontains=search_query) |
                Q(user__last_name__icontains=search_query) |
                Q(user__username__icontains=search_query) |
                Q(message__icontains=search_query)
            )
            
        # Apply date filters
        if from_date:
            try:
                from_datetime = datetime.strptime(from_date, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                join_requests = join_requests.filter(created_at__gte=from_datetime)
            except ValueError:
                pass
                
        if to_date:
            try:
                to_datetime = datetime.strptime(to_date, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                to_datetime = to_datetime.replace(hour=23, minute=59, second=59)
                join_requests = join_requests.filter(created_at__lte=to_datetime)
            except ValueError:
                pass
                
        # Apply ordering
        order_prefix = "-" if order_direction == "desc" else ""
        order_field = f"{order_prefix}{order_by}"
        join_requests = join_requests.order_by(order_field)
        
        # Apply pagination
        paginator = self.pagination_class()
        paginated_requests = paginator.paginate_queryset(join_requests, request, self)
        
        # Serialize the data
        serializer = JoinRequestDetailSerializer(paginated_requests, many=True, context={'request': request})
        
        return paginator.get_paginated_response(
            serializer.data, message="Join requests retrieved successfully"
        )
