from django.contrib import admin

from .models import JoinRequest


@admin.register(JoinRequest)
class JoinRequestAdmin(admin.ModelAdmin):
    list_display = (
        "id",
        "organization",
        "user",
        "status",
        "created_at",
        "updated_at",
    )
    list_filter = ("status",)
    search_fields = (
        "organization__name",
        "user__email",
        "user__first_name",
        "user__last_name",
    )
    readonly_fields = ("id", "created_at", "updated_at")
    ordering = ("-created_at",)
