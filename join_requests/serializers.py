from rest_framework import serializers

from jechspace_backend.utils import ErrorCodes
from organizations.models import Organization
from organizations.serializers import OrganizationListSerializer
from users.serializers import UserSerializer
from users.models import Roles, UserOrganization

from .models import JoinRequest


class JoinRequestSerializer(serializers.ModelSerializer):
    """
    Serializer for the JoinRequest model
    """
    organization_id = serializers.UUIDField(write_only=True)
    user = UserSerializer(read_only=True)
    organization = serializers.SerializerMethodField()
    reviewed_by = UserSerializer(read_only=True)

    class Meta:
        model = JoinRequest
        fields = [
            "id",
            "organization_id",
            "organization",
            "user",
            "message",
            "status",
            "reviewed_by",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "organization",
            "user",
            "status",
            "reviewed_by",
            "created_at",
            "updated_at",
        ]

    def get_organization(self, obj):
        """Return minimal organization data"""
        return {
            "id": str(obj.organization.id),
            "name": obj.organization.name,
            "logo": obj.organization.logo.url if obj.organization.logo else None,
        }

    def validate_organization_id(self, value):
        """
        Ensure the organization exists and is public
        """
        try:
            organization = Organization.objects.get(id=value)
            if organization.type != Organization.OrganizationType.PUBLIC:
                raise serializers.ValidationError(
                    "Cannot request to join a private organization.",
                    code=ErrorCodes.ORGANIZATION_NOT_FOUND,
                )
            self.context['organization'] = organization
            return value
        except Organization.DoesNotExist:
            raise serializers.ValidationError(
                "Organization not found.", code=ErrorCodes.ORGANIZATION_NOT_FOUND
            )

    def validate(self, data):
        """
        Validate that the user is not already a member of the organization
        and doesn't have a pending join request
        """
        user = self.context.get("request").user
        organization_id = data.get("organization_id")
        organization = self.context.get("organization")

        # Check if user is already a member
        if user.organizations.filter(organization_id=organization_id).exists():
            raise serializers.ValidationError(
                "You are already a member of this organization.",
                code=ErrorCodes.USER_ALREADY_IN_ORGANIZATION,
            )

        # Check if user already has a pending join request
        if JoinRequest.objects.filter(
            user=user,
            organization=organization,
            status=JoinRequest.JoinRequestStatus.PENDING,
        ).exists():
            raise serializers.ValidationError(
                "You already have a pending join request for this organization.",
                code=ErrorCodes.JOIN_REQUEST_ALREADY_EXISTS,
            )

        return data

    def create(self, validated_data):
        """
        Create a new join request
        """
        request = self.context.get("request")
        organization = self.context.get("organization")

        validated_data["organization"] = organization
        validated_data["user"] = request.user
        validated_data["status"] = JoinRequest.JoinRequestStatus.PENDING

        return super().create(validated_data)


class JoinRequestDetailSerializer(JoinRequestSerializer):
    """
    Enhanced serializer for detailed view of a JoinRequest
    """
    organization = OrganizationListSerializer(read_only=True)
    user = UserSerializer(read_only=True)
    reviewed_by = UserSerializer(read_only=True)
    time_pending = serializers.SerializerMethodField()

    class Meta:
        model = JoinRequest
        fields = [
            "id",
            "organization",
            "user",
            "message",
            "status",
            "reviewed_by",
            "created_at",
            "updated_at",
            "time_pending",
        ]
        read_only_fields = fields

    def get_time_pending(self, obj):
        """
        Calculate how long the request has been pending
        """
        import django.utils.timezone
        from django.utils.timesince import timesince
        
        if obj.status == JoinRequest.JoinRequestStatus.PENDING:
            return timesince(obj.created_at)
        return None


class JoinRequestUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating join requests (approve/reject)
    """

    class Meta:
        model = JoinRequest
        fields = ["status", "role", "department"]
        extra_kwargs = {
            "status": {"required": True},
            "role": {"required": True},
            "department": {"required": False},
        }

    def validate_status(self, value):
        """Validate status is either approved or rejected"""
        if value not in [JoinRequest.JoinRequestStatus.APPROVED, JoinRequest.JoinRequestStatus.REJECTED]:
            raise serializers.ValidationError(
                "Status must be either 'approved' or 'rejected'",
                code=ErrorCodes.INVALID_FORMAT,
            )
        return value

    def validate(self, data):
        """Validate role and department if status is approved"""
        if data.get("status") == JoinRequest.JoinRequestStatus.APPROVED:
            if data.get("role") != Roles.MEMBER and not data.get("department"):
                raise serializers.ValidationError(
                    "Employee must belong to a department",
                    code=ErrorCodes.INVALID_FORMAT,
                )
        return data
    
    def update(self, instance, validated_data):
        """
        Update the join request and create user-organization relationship if approved
        """
        # Update the join request
        instance.status = validated_data.get("status", instance.status)
        instance.reviewed_by = self.context.get("request").user
        instance.save()

        # If approved, create user-organization relationship
        if instance.status == JoinRequest.JoinRequestStatus.APPROVED:
            # Create user-organization relationship
            UserOrganization.objects.create(
                user=instance.user,
                organization=instance.organization,
                role=validated_data.get("role", Roles.MEMBER),
                department=validated_data.get("department", None),
            )

        return instance


class BulkJoinRequestUpdateSerializer(serializers.Serializer):
    """
    Serializer for bulk updating join requests
    """
    join_request_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=True,
        min_length=1
    )
    status = serializers.ChoiceField(
        choices=[JoinRequest.JoinRequestStatus.APPROVED, JoinRequest.JoinRequestStatus.REJECTED],
        required=True
    )
    role = serializers.CharField(required=False)
    department = serializers.UUIDField(required=False)
    
    def validate(self, data):
        """Validate the ids exist and belong to the organization"""
        organization_id = self.context.get('organization_id')
        join_request_ids = data.get('join_request_ids', [])
        
        # Check if all join requests exist and belong to the organization
        existing_requests = JoinRequest.objects.filter(
            id__in=join_request_ids,
            organization_id=organization_id,
            status=JoinRequest.JoinRequestStatus.PENDING
        )
        
        if len(existing_requests) != len(join_request_ids):
            found_ids = set(str(req.id) for req in existing_requests)
            missing_ids = [str(req_id) for req_id in join_request_ids if str(req_id) not in found_ids]
            
            raise serializers.ValidationError(
                {
                    "join_request_ids": f"Some join requests are invalid or already processed: {', '.join(missing_ids)}",
                    "code": ErrorCodes.JOIN_REQUEST_NOT_FOUND
                }
            )
            
        # Validate role and department if status is approved
        if data.get("status") == JoinRequest.JoinRequestStatus.APPROVED:
            if data.get("role") != Roles.MEMBER and not data.get("department"):
                raise serializers.ValidationError(
                    {
                        "department": "Department is required for non-member roles",
                        "code": ErrorCodes.INVALID_FORMAT
                    }
                )
                
        return data


class JoinRequestCancelSerializer(serializers.ModelSerializer):
    """
    Serializer for canceling a join request with optional reason
    """
    cancellation_reason = serializers.CharField(required=False, allow_blank=True)
    
    class Meta:
        model = JoinRequest
        fields = ["cancellation_reason"]
