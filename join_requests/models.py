from django.conf import settings
from django.db import models
from core.models import BaseModel

from organizations.models import Organization


class JoinRequest(BaseModel):
    """
    Join request model for users requesting to join an organization.
    """

    class JoinRequestStatus:
        PENDING = "pending"
        APPROVED = "approved"
        REJECTED = "rejected"

        CHOICES = (
            (PENDING, "Pending"),
            (APPROVED, "Approved"),
            (REJECTED, "Rejected"),
        )

    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name="join_requests"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="join_requests"
    )
    message = models.TextField(blank=True, null=True)
    status = models.CharField(
        max_length=10,
        choices=JoinRequestStatus.CHOICES,
        default=JoinRequestStatus.PENDING,
    )
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        related_name="reviewed_join_requests",
        null=True,
        blank=True,
    )

    class Meta:
        unique_together = ("user", "organization", "status")

    def __str__(self):
        return f"{self.user} - {self.organization} - {self.status}"
