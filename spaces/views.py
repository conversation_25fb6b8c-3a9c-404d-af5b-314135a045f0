from rest_framework import status, viewsets
from rest_framework.decorators import action

from amenities.models import Amenity
from amenities.serializers import AmenitySerializer
from jechspace_backend.utils import ErrorCodes, api_response
from organizations.mixins import OrganizationPermissionMixin
from permissions.constants import PermissionActions, PermissionCategories

from .models import Space
from .serializers import SpaceBookingPolicySerializer, SpaceSerializer


class SpaceViewSet(OrganizationPermissionMixin, viewsets.ModelViewSet):
    """
    A viewset that provides full CRUD operations for managing Spaces.

    Includes:
    - Listing spaces filtered by organization
    - Creating new spaces
    - Updating and deleting specific space instances
    - Custom actions for booking policy update, amenity management, and availability lookup

    Permissions:
    - Requires organization context
    - Admin-only access for mutating operations
    """

    queryset = Space.objects.all()
    serializer_class = SpaceSerializer
    permission_category = PermissionCategories.SPACE
    permission_action = PermissionActions.MANAGE

    def get_queryset(self):
        """
        Filters the queryset to return only spaces associated with the authenticated user's organization.
        """
        if getattr(self, "swagger_fake_view", False):
            return Space.objects.none()

        organization = self.request.organization
        self.permission_action = PermissionActions.VIEW

        return Space.objects.filter(location__organization=organization)

    def create(self, request, *args, **kwargs):
        """
        Creates a new space with optional nested `availability`, `booking_policy`, and `amenities`.

        Requires:
        - Organization context from request
        - Validated space creation payload
        """
        self.permission_action = PermissionActions.CREATE

        serializer = self.get_serializer(
            data=request.data, context={"request": request}
        )
        serializer.is_valid(raise_exception=True)
        instance = serializer.save()

        return api_response(
            data=self.get_serializer(instance).data,
            message="Space created successfully",
        )

    def update(self, request, *args, **kwargs):
        """
        Partially updates a space instance.

        Accepts any valid subset of the space model fields.
        """
        self.permission_action = PermissionActions.UPDATE

        partial = kwargs.pop("partial", True)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return api_response(data=serializer.data, message="Space updated successfully")

    def destroy(self, request, *args, **kwargs):
        """
        Deletes a space instance from the system.
        """
        self.permission_action = PermissionActions.DELETE

        instance = self.get_object()
        self.perform_destroy(instance)
        return api_response(message="Space deleted successfully")

    @action(methods=["patch"], detail=True, url_path="booking-policy")
    def update_policy(self, request, pk=None):
        """
        PATCH /spaces/{space_id}/booking-policy/

        Updates the booking policy associated with a space.
        Allows partial updates of fields such as duration, intervals, and approval requirements.
        """
        space = self.get_object()
        policy = getattr(space, "booking_policy", None)

        if not policy:
            return api_response(
                message="Booking policy not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )

        serializer = SpaceBookingPolicySerializer(
            policy, data=request.data, partial=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return api_response(
            data=serializer.data, message="Booking policy updated successfully"
        )

    @action(methods=["patch"], detail=True, url_path="amenities")
    def update_amenities(self, request, pk=None):
        """
        PATCH /spaces/{space_id}/amenities/

        Adds or removes an amenity from a space.
        Requires:
        - `amenity_id`: UUID of the amenity
        - `action`: either "add" or "remove"
        """
        space = self.get_object()
        amenity_id = request.data.get("amenity_id")
        action_type = request.data.get("action")

        try:
            amenity = Amenity.objects.get(id=amenity_id)
        except Amenity.DoesNotExist:
            return api_response(
                message="Amenity not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )

        if action_type == "add":
            space.amenities.add(amenity)
            message = "Amenity added to space successfully"
        elif action_type == "remove":
            space.amenities.remove(amenity)
            message = "Amenity removed from space successfully"
        else:
            return api_response(
                message="Invalid action",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
            )

        return api_response(
            message=message,
            data=AmenitySerializer(amenity).data if action_type == "add" else None,
        )

    @action(methods=["get"], detail=True, url_path="availability")
    def get_availability(self, request, pk=None):
        """
        GET /spaces/{space_id}/availability/?start_date=YYYY-MM-DD&end_date=YYYY-MM-DD

        Retrieves the availability of a space between the given date range.
        Currently returns static availability based on `SpaceAvailability` model.
        Future improvement: include booked slots and conflict detection.
        """
        from_date = request.query_params.get("start_date")
        to_date = request.query_params.get("end_date")

        space = self.get_object()
        availabilities = space.availability.all()

        data = []
        for a in availabilities:
            data.append(
                {
                    "day_of_week": a.day,
                    "open_time": a.open_time.strftime("%H:%M"),
                    "close_time": a.close_time.strftime("%H:%M"),
                }
            )

        return api_response(
            message="Space availability retrieved successfully",
            data={
                "space_id": str(space.id),
                "space_name": space.name,
                "availability": data,
            },
        )
