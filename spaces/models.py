import uuid

from django.conf import settings
from django.core.validators import MinValueValidator
from django.db import models
from django.utils import timezone


class DayOfWeekChoices(models.TextChoices):
    """Enum for days of the week, used in availability."""

    MONDAY = "monday", "Monday"
    TUESDAY = "tuesday", "Tuesday"
    WEDNESDAY = "wednesday", "Wednesday"
    THURSDAY = "thursday", "Thursday"
    FRIDAY = "friday", "Friday"
    SATURDAY = "saturday", "Saturday"
    SUNDAY = "sunday", "Sunday"


class SpaceStatusChoices(models.TextChoices):
    """Enum for space availability status."""

    AVAILABLE = "available", "Available"
    RESERVED = "reserved", "Reserved"
    UNDER_MAINTENANCE = "under_maintenance", "Under Maintenance"
    INACTIVE = "inactive", "Inactive"


class SpaceTypeChoices(models.TextChoices):
    """Enum for types of spaces."""

    MEETING_ROOM = "meeting_room", "Meeting Room"
    DESK = "desk", "Desk"
    OFFICE = "office", "Office"
    EVENT_SPACE = "event_space", "Event Space"
    KITCHEN = "kitchen", "Kitchen"


class Space(models.Model):
    """
    Represents a physical space that can be booked.
    Contains business-relevant data like capacity, type, and location.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    slug = models.SlugField(max_length=255)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    space_type = models.CharField(
        max_length=50, choices=SpaceTypeChoices.choices, default=SpaceTypeChoices.DESK
    )
    location = models.ForeignKey(
        "locations.Location", on_delete=models.CASCADE, related_name="spaces"
    )

    capacity = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    floor = models.CharField(max_length=100)
    room = models.CharField(max_length=100, null=True, blank=True)
    desk = models.CharField(max_length=100, null=True, blank=True)

    status = models.CharField(
        max_length=20,
        choices=SpaceStatusChoices.choices,
        default=SpaceStatusChoices.AVAILABLE,
    )

    amenities = models.ManyToManyField("amenities.Amenity", blank=True)
    images = models.JSONField(default=list, blank=True)
    floor_plan = models.URLField(blank=True, null=True)

    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        related_name="created_spaces",
        default=1,
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["slug"]
        indexes = [
            models.Index(fields=["slug", "is_active"]),
            models.Index(fields=["location", "is_active"]),
        ]
        unique_together = [("location", "slug")]

    def __str__(self):
        return self.name

    def is_available(self):
        """
        Business logic: Determines if the space is available for booking.
        """
        return self.status == SpaceStatusChoices.AVAILABLE


class SpaceAvailability(models.Model):
    """
    Defines the available time slots for a space on a per-day basis.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    space = models.ForeignKey(
        Space, on_delete=models.CASCADE, related_name="availability"
    )
    day = models.CharField(max_length=10, choices=DayOfWeekChoices.choices)
    open_time = models.TimeField()
    close_time = models.TimeField()

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ["day", "open_time"]
        verbose_name_plural = "Space Availabilities"

    def __str__(self):
        return f"{self.space.name} - {self.day} ({self.open_time}-{self.close_time})"


class SpaceBookingPolicy(models.Model):
    """
    Defines the rules for booking a space: duration, intervals, and approval.
    """

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    space = models.OneToOneField(
        Space, on_delete=models.CASCADE, related_name="booking_policy"
    )

    min_duration_minutes = models.PositiveIntegerField(
        validators=[MinValueValidator(1)]
    )
    max_duration_minutes = models.PositiveIntegerField()
    booking_interval_minutes = booking_interval_minutes = models.PositiveIntegerField(
        null=True, blank=True
    )  # models.PositiveIntegerField()
    advance_booking_minutes = models.PositiveIntegerField()

    cancel_before_minutes = models.PositiveIntegerField(null=True, blank=True)
    requires_approval = models.BooleanField(default=False)
    requires_approval_for_changes = models.BooleanField(default=False)
    allow_recurring_bookings = models.BooleanField(default=False)
    max_recurring_instances = models.PositiveIntegerField(null=True, blank=True)

    created_at = created_at = models.DateTimeField(
        auto_now_add=True
    )  # models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Space Booking Policies"

    def __str__(self):
        return f"Booking Policy for {self.space.name}"

    def is_valid_duration(self, duration_minutes):
        """
        Business logic: Validates if a given duration is allowed.
        """
        return (
            self.min_duration_minutes <= duration_minutes <= self.max_duration_minutes
        )
