# Generated by Django 5.2 on 2025-06-03 12:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("locations", "0002_initial"),
        ("spaces", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="space",
            name="created_by",
            field=models.ForeignKey(
                default=1,
                on_delete=django.db.models.deletion.PROTECT,
                related_name="created_spaces",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="space",
            name="location",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="spaces",
                to="locations.location",
            ),
        ),
        migrations.AddField(
            model_name="spaceavailability",
            name="space",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="availability",
                to="spaces.space",
            ),
        ),
        migrations.AddField(
            model_name="spacebookingpolicy",
            name="space",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="booking_policy",
                to="spaces.space",
            ),
        ),
        migrations.AddIndex(
            model_name="space",
            index=models.Index(
                fields=["slug", "is_active"], name="spaces_spac_slug_958070_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="space",
            index=models.Index(
                fields=["location", "is_active"], name="spaces_spac_locatio_039af4_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="space",
            unique_together={("location", "slug")},
        ),
    ]
