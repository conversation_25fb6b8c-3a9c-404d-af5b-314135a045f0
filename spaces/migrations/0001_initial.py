# Generated by Django 5.2 on 2025-06-03 12:56

import uuid

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("amenities", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="SpaceAvailability",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "day",
                    models.CharField(
                        choices=[
                            ("monday", "Monday"),
                            ("tuesday", "Tuesday"),
                            ("wednesday", "Wednesday"),
                            ("thursday", "Thursday"),
                            ("friday", "Friday"),
                            ("saturday", "Saturday"),
                            ("sunday", "Sunday"),
                        ],
                        max_length=10,
                    ),
                ),
                ("open_time", models.TimeField()),
                ("close_time", models.TimeField()),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "Space Availabilities",
                "ordering": ["day", "open_time"],
            },
        ),
        migrations.CreateModel(
            name="SpaceBookingPolicy",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "min_duration_minutes",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)]
                    ),
                ),
                ("max_duration_minutes", models.PositiveIntegerField()),
                (
                    "booking_interval_minutes",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("advance_booking_minutes", models.PositiveIntegerField()),
                (
                    "cancel_before_minutes",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("requires_approval", models.BooleanField(default=False)),
                ("requires_approval_for_changes", models.BooleanField(default=False)),
                ("allow_recurring_bookings", models.BooleanField(default=False)),
                (
                    "max_recurring_instances",
                    models.PositiveIntegerField(blank=True, null=True),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name_plural": "Space Booking Policies",
            },
        ),
        migrations.CreateModel(
            name="Space",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("slug", models.SlugField(max_length=255)),
                ("name", models.CharField(max_length=255)),
                ("description", models.TextField(blank=True)),
                (
                    "space_type",
                    models.CharField(
                        choices=[
                            ("meeting_room", "Meeting Room"),
                            ("desk", "Desk"),
                            ("office", "Office"),
                            ("event_space", "Event Space"),
                            ("kitchen", "Kitchen"),
                        ],
                        default="desk",
                        max_length=50,
                    ),
                ),
                (
                    "capacity",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)]
                    ),
                ),
                ("floor", models.CharField(max_length=100)),
                ("room", models.CharField(blank=True, max_length=100, null=True)),
                ("desk", models.CharField(blank=True, max_length=100, null=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("available", "Available"),
                            ("reserved", "Reserved"),
                            ("under_maintenance", "Under Maintenance"),
                            ("inactive", "Inactive"),
                        ],
                        default="available",
                        max_length=20,
                    ),
                ),
                ("images", models.JSONField(blank=True, default=list)),
                ("floor_plan", models.URLField(blank=True, null=True)),
                ("is_active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "amenities",
                    models.ManyToManyField(blank=True, to="amenities.amenity"),
                ),
            ],
            options={
                "ordering": ["slug"],
            },
        ),
    ]
