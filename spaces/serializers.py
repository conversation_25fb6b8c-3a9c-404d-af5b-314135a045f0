
from rest_framework import serializers
from .models import (
    Space,
    SpaceAvailability,
    SpaceBookingPolicy,
    SpaceStatusChoices,
    SpaceTypeChoices
)
from amenities.models import Amenity


class SpaceAvailabilitySerializer(serializers.ModelSerializer):
    """
    Serializer for space availability slots.
    """

    class Meta:
        model = SpaceAvailability
        fields = ["day", "open_time", "close_time"]


class SpaceBookingPolicySerializer(serializers.ModelSerializer):
    """
    Serializer for booking policies related to a space.
    """

    class Meta:
        model = SpaceBookingPolicy
        fields = [
            "min_duration_minutes",
            "max_duration_minutes",
            "booking_interval_minutes",
            "advance_booking_minutes",
            "cancel_before_minutes",
            "requires_approval",
            "requires_approval_for_changes",
            "allow_recurring_bookings",
            "max_recurring_instances",
            "created_at",
            "updated_at"
        ]
        read_only_fields = ["created_at", "updated_at"]


class SpaceSerializer(serializers.ModelSerializer):
    """
    Main serializer for the Space model.
    Handles nested relationships and presentation logic via method fields.
    """

    space_type = serializers.ChoiceField(choices=SpaceTypeChoices.choices)
    status = serializers.ChoiceField(choices=SpaceStatusChoices.choices)
    amenities = serializers.PrimaryKeyRelatedField(
        queryset=Amenity.objects.all(), many=True, required=False
    )
    booking_policy = SpaceBookingPolicySerializer(required=False)
    availability = SpaceAvailabilitySerializer(many=True, required=False)
    address = serializers.SerializerMethodField()
    opening_hours = serializers.SerializerMethodField()

    class Meta:
        model = Space
        fields = [
            "id",
            "slug",
            "name",
            "description",
            "space_type",
            "status",
            "capacity",
            "address",
            "amenities",
            "images",
            "floor_plan",
            "is_active",
            "location",
            "created_by",
            "created_at",
            "updated_at",
            "booking_policy",
            "availability",
            "opening_hours"
        ]
        read_only_fields = ["created_by", "created_at", "updated_at", "opening_hours"]

    def get_address(self, obj):
        """
        Presentation logic: Return address as a nested dictionary.
        """
        return {
            "floor": obj.floor,
            "room": obj.room,
            "desk": obj.desk,
        }

    def get_opening_hours(self, obj):
        """
        Presentation logic: Return availability grouped by day.
        """
        result = {}
        for slot in obj.availability.all():
            day = slot.day
            if day not in result:
                result[day] = []
            result[day].append({
                "open": slot.open_time.strftime("%H:%M"),
                "close": slot.close_time.strftime("%H:%M")
            })
        return result

    def create(self, validated_data):
        """
        Handle creation of nested booking policy and availability.
        """
        amenities = validated_data.pop("amenities", [])
        availability_data = validated_data.pop("availability", [])
        booking_policy_data = validated_data.pop("booking_policy", {})
        validated_data["created_by"] = self.context["request"].user

        space = Space.objects.create(**validated_data)
        space.amenities.set(amenities)

        if availability_data:
            SpaceAvailability.objects.bulk_create([
                SpaceAvailability(space=space, **slot) for slot in availability_data
            ])

        if booking_policy_data:
            SpaceBookingPolicy.objects.create(space=space, **booking_policy_data)

        return space