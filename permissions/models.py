from django.db import models

from core.models import BaseModel

from .constants import PermissionCategories, PermissionCodes


class PermissionType(BaseModel):
    """
    Defines the types of permissions available in the system.
    Each permission type represents a category of actions that can be performed.
    """

    name = models.CharField(max_length=100)
    code = models.CharField(
        max_length=100, unique=True, choices=PermissionCodes.CHOICES
    )
    description = models.TextField()
    category = models.CharField(max_length=100, choices=PermissionCategories.CHOICES)

    def __str__(self):
        return self.name


class DepartmentPermission(BaseModel):
    """
    Assigns permissions to departments with specific action levels.
    This creates a many-to-many relationship between departments and permission types
    with additional action-level controls.
    """

    department = models.ForeignKey("departments.Department", on_delete=models.CASCADE, related_name="permissions")
    permission_type = models.ForeignKey(PermissionType, on_delete=models.CASCADE)

    # Action levels
    can_view = models.BooleanField(default=False)
    can_create = models.BooleanField(default=False)
    can_update = models.BooleanField(default=False)
    can_delete = models.BooleanField(default=False)
    can_manage = models.BooleanField(default=False)

    class Meta:
        unique_together = ("department", "permission_type")

    def __str__(self):
        return f"{self.department.name} - {self.permission_type.name}"
