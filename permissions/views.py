from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from departments.models import Department
from jechspace_backend.utils import ErrorCodes, api_response
from organizations.mixins import OrganizationPermissionMixin
from permissions.constants import PermissionActions, PermissionCategories

from .models import DepartmentPermission, PermissionType
from .serializers import DepartmentPermissionSerializer, PermissionTypeSerializer


class PermissionTypeListView(OrganizationPermissionMixin, APIView):
    """
    API view for listing permission types
    """

    permission_category = PermissionCategories.ORGANIZATION
    permission_action = PermissionActions.MANAGE

    @swagger_auto_schema(
        operation_summary="List all permission types",
        operation_description="Lists all available permission types in the system.",
        responses={
            200: openapi.Response(
                description="Permission types retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Permission types retrieved successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    "id": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="550e8400-e29b-41d4-a716-446655440000",
                                    ),
                                    "code": openapi.Schema(
                                        type=openapi.TYPE_STRING, example="booking"
                                    ),
                                    "name": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="Booking Management",
                                    ),
                                    "description": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="Permissions for booking management",
                                    ),
                                },
                            ),
                        ),
                    },
                ),
            ),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Permissions"],
    )
    def get(self, request):
        """
        List all permission types
        """
        permission_types = PermissionType.objects.all()
        serializer = PermissionTypeSerializer(permission_types, many=True)

        return api_response(
            data=serializer.data,
            message="Permission types retrieved successfully",
            status_code=status.HTTP_200_OK,
        )


class DepartmentPermissionListCreateView(OrganizationPermissionMixin, APIView):
    """
    API view for listing and creating department permissions
    """

    permission_category = PermissionCategories.DEPARTMENT
    permission_action = PermissionActions.MANAGE

    @swagger_auto_schema(
        operation_summary="List department permissions",
        operation_description="Lists all permissions assigned to a specific department.",
        responses={
            200: openapi.Response(
                description="Department permissions retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Department permissions retrieved successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    "id": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="550e8400-e29b-41d4-a716-446655440000",
                                    ),
                                    "department": openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="550e8400-e29b-41d4-a716-446655440000",
                                    ),
                                    "permission_type": openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            "id": openapi.Schema(
                                                type=openapi.TYPE_STRING,
                                                example="550e8400-e29b-41d4-a716-446655440000",
                                            ),
                                            "code": openapi.Schema(
                                                type=openapi.TYPE_STRING,
                                                example="booking",
                                            ),
                                            "name": openapi.Schema(
                                                type=openapi.TYPE_STRING,
                                                example="Booking Management",
                                            ),
                                        },
                                    ),
                                    "can_view": openapi.Schema(
                                        type=openapi.TYPE_BOOLEAN, example=True
                                    ),
                                    "can_create": openapi.Schema(
                                        type=openapi.TYPE_BOOLEAN, example=True
                                    ),
                                    "can_update": openapi.Schema(
                                        type=openapi.TYPE_BOOLEAN, example=False
                                    ),
                                    "can_delete": openapi.Schema(
                                        type=openapi.TYPE_BOOLEAN, example=False
                                    ),
                                    "can_manage": openapi.Schema(
                                        type=openapi.TYPE_BOOLEAN, example=False
                                    ),
                                },
                            ),
                        ),
                    },
                ),
            ),
            404: openapi.Response(description="Department not found"),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Permissions"],
    )
    def get(self, request, organization_id, department_id):
        """
        List all permissions for a department
        """
        try:
            # Verify the department belongs to this organization
            department = Department.objects.get(
                id=department_id, organization_id=organization_id
            )
            permissions = DepartmentPermission.objects.filter(department=department)
            serializer = DepartmentPermissionSerializer(permissions, many=True)

            return api_response(
                data=serializer.data,
                message="Department permissions retrieved successfully",
                status_code=status.HTTP_200_OK,
            )
        except Department.DoesNotExist:
            return api_response(
                errors={
                    "department": {
                        "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                        "message": "Department not found",
                    }
                },
                message="Department not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )

    @swagger_auto_schema(
        operation_summary="Create department permission",
        operation_description="Creates a new permission for a specific department.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["permission_type_id"],
            properties={
                "permission_type_id": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    example="550e8400-e29b-41d4-a716-446655440000",
                ),
                "can_view": openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                "can_create": openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                "can_update": openapi.Schema(type=openapi.TYPE_BOOLEAN, example=False),
                "can_delete": openapi.Schema(type=openapi.TYPE_BOOLEAN, example=False),
                "can_manage": openapi.Schema(type=openapi.TYPE_BOOLEAN, example=False),
            },
        ),
        responses={
            201: openapi.Response(
                description="Department permission created successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Department permission created successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "department": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "permission_type": openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    properties={
                                        "id": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="550e8400-e29b-41d4-a716-446655440000",
                                        ),
                                        "code": openapi.Schema(
                                            type=openapi.TYPE_STRING, example="booking"
                                        ),
                                        "name": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="Booking Management",
                                        ),
                                    },
                                ),
                                "can_view": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=True
                                ),
                                "can_create": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=True
                                ),
                                "can_update": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=False
                                ),
                                "can_delete": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=False
                                ),
                                "can_manage": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=False
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Invalid data"),
            404: openapi.Response(description="Department not found"),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Permissions"],
    )
    def post(self, request, organization_id, department_id):
        """
        Create a new permission for a department
        """
        try:
            # Verify the department belongs to this organization
            department = Department.objects.get(
                id=department_id, organization_id=organization_id
            )

            # Add department to serializer context
            serializer = DepartmentPermissionSerializer(
                data=request.data, context={"department": department}
            )

            if serializer.is_valid():
                serializer.save()
                return api_response(
                    data=serializer.data,
                    message="Department permission created successfully",
                    status_code=status.HTTP_201_CREATED,
                )
            return api_response(
                errors=serializer.errors,
                message="Invalid data",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
            )
        except Department.DoesNotExist:
            return api_response(
                errors={
                    "department": {
                        "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                        "message": "Department not found",
                    }
                },
                message="Department not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )


class DepartmentPermissionDetailView(OrganizationPermissionMixin, APIView):
    """
    API view for updating and deleting department permissions
    """

    permission_category = PermissionCategories.DEPARTMENT
    permission_action = PermissionActions.MANAGE

    @swagger_auto_schema(
        operation_summary="Update department permission",
        operation_description="Updates an existing permission for a specific department.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "can_view": openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                "can_create": openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                "can_update": openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                "can_delete": openapi.Schema(type=openapi.TYPE_BOOLEAN, example=False),
                "can_manage": openapi.Schema(type=openapi.TYPE_BOOLEAN, example=False),
            },
        ),
        responses={
            200: openapi.Response(
                description="Department permission updated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Department permission updated successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "department": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "permission_type": openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    properties={
                                        "id": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="550e8400-e29b-41d4-a716-446655440000",
                                        ),
                                        "code": openapi.Schema(
                                            type=openapi.TYPE_STRING, example="booking"
                                        ),
                                        "name": openapi.Schema(
                                            type=openapi.TYPE_STRING,
                                            example="Booking Management",
                                        ),
                                    },
                                ),
                                "can_view": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=True
                                ),
                                "can_create": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=True
                                ),
                                "can_update": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=True
                                ),
                                "can_delete": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=False
                                ),
                                "can_manage": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=False
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(description="Invalid data"),
            404: openapi.Response(description="Department or permission not found"),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Permissions"],
    )
    def patch(self, request, organization_id, department_id, permission_id):
        """
        Update a department permission
        """
        try:
            # Verify the department belongs to this organization
            department = Department.objects.get(
                id=department_id, organization_id=organization_id
            )

            # Retrieve the permission
            try:
                department_permission = DepartmentPermission.objects.get(
                    id=permission_id, department=department
                )
            except DepartmentPermission.DoesNotExist:
                return api_response(
                    errors={
                        "permission": {
                            "code": "permission_not_found",
                            "message": "Permission not found for this department",
                        }
                    },
                    message="Permission not found",
                    status_code=status.HTTP_404_NOT_FOUND,
                    success=False,
                )

            # Update fields that can be modified: can_update, can_approve, can_create, can_delete
            update_fields = {}
            for field in [
                "can_update",
                "can_view",
                "can_create",
                "can_delete",
                "can_manage",
            ]:
                if field in request.data:
                    update_fields[field] = request.data[field]

            serializer = DepartmentPermissionSerializer(
                department_permission, data=update_fields, partial=True
            )

            if serializer.is_valid():
                serializer.save()
                return api_response(
                    data=serializer.data,
                    message="Department permission updated successfully",
                    status_code=status.HTTP_200_OK,
                )
            return api_response(
                errors=serializer.errors,
                message="Invalid data",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
            )
        except Department.DoesNotExist:
            return api_response(
                errors={
                    "department": {
                        "code": ErrorCodes.ORGANIZATION_NOT_FOUND,
                        "message": "Department not found",
                    }
                },
                message="Department not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )
