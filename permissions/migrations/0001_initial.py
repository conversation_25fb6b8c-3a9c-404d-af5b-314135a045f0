# Generated by Django 5.2 on 2025-06-03 12:56

import uuid

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("departments", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="PermissionType",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("name", models.CharField(max_length=100)),
                (
                    "code",
                    models.CharField(
                        choices=[
                            ("organization", "Organization"),
                            ("amenities", "Amenities"),
                            ("department", "Department"),
                            ("user", "User"),
                            ("content", "Content"),
                            ("settings", "Settings"),
                            ("location", "Location"),
                            ("space", "Space"),
                            ("booking", "Booking"),
                            ("invitation", "Invitation"),
                            ("report", "Report"),
                        ],
                        max_length=100,
                        unique=True,
                    ),
                ),
                ("description", models.TextField()),
                (
                    "category",
                    models.CharField(
                        choices=[
                            ("organization", "Organization"),
                            ("amenities", "Amenities"),
                            ("department", "Department"),
                            ("user", "User"),
                            ("content", "Content"),
                            ("settings", "Settings"),
                            ("location", "Location"),
                            ("space", "Space"),
                            ("booking", "Booking"),
                            ("invitation", "Invitation"),
                            ("report", "Report"),
                        ],
                        max_length=100,
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="DepartmentPermission",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("can_view", models.BooleanField(default=False)),
                ("can_create", models.BooleanField(default=False)),
                ("can_update", models.BooleanField(default=False)),
                ("can_delete", models.BooleanField(default=False)),
                ("can_manage", models.BooleanField(default=False)),
                (
                    "department",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="permissions",
                        to="departments.department",
                    ),
                ),
                (
                    "permission_type",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to="permissions.permissiontype",
                    ),
                ),
            ],
            options={
                "unique_together": {("department", "permission_type")},
            },
        ),
    ]
