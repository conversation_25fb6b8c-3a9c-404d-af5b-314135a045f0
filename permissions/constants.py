class PermissionCategories:
    """
    Categories for grouping permissions
    """

    ORGANIZATION = "organization"
    AMENITIES = "amenities"
    DEPARTMENT = "department"
    USER = "user"
    CONTENT = "content"
    SETTINGS = "settings"
    LOCATION = "location"
    SPACE = "space"
    BOOKING = "booking"
    INVITATION = "invitation"
    REPORT = "report"

    CHOICES = (
        (ORGANIZATION, "Organization"),
        (AMENITIES, "Amenities"),
        (DEPARTMENT, "Department"),
        (USER, "User"),
        (CONTENT, "Content"),
        (SETTINGS, "Settings"),
        (LOCATION, "Location"),
        (SPACE, "Space"),
        (BOOKING, "Booking"),
        (INVITATION, "Invitation"),
        (REPORT, "Report"),
    )


class PermissionActions:
    """
    Standard actions that can be performed on any permission category
    """

    VIEW = "view"
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    MANAGE = "manage"

    CHOICES = (
        (VIEW, "View"),
        (CREATE, "Create"),
        (UPDATE, "Update"),
        (DELETE, "Delete"),
        (MANAGE, "Manage"),
    )


class PermissionCodes(PermissionCategories):
    """
    Permission codes for the system - using categories as codes
    """


# Hardcoded permissions for member role users
MEMBER_PERMISSIONS = {
    "organization": {
        "view": True,  # Can view their organization
        "create": False,  # Cannot create organizations
        "modify": False,  # Cannot modify organization
        "delete": False,  # Cannot delete organization
    },
    "user": {
        "view": True,  # Can view users in their organization
        "create": False,  # Cannot create users
        "modify": False,  # Cannot modify users
        "delete": False,  # Cannot delete users
    },
    "booking": {
        "view": True,  # Can view bookings
        "create": True,  # Can create bookings
        "modify": True,  # Can modify their own bookings
        "delete": True,  # Can delete their own bookings
    },
    "space": {
        "view": True,  # Can view spaces
        "create": False,  # Cannot create spaces
        "modify": False,  # Cannot modify spaces
        "delete": False,  # Cannot delete spaces
    },
    "location": {
        "view": True,  # Can view locations
        "create": False,  # Cannot create locations
        "modify": False,  # Cannot modify locations
        "delete": False,  # Cannot delete locations
    },
    "invitation": {
        "view": True,  # Can view their own invitations
        "create": False,  # Cannot create invitations
        "modify": True,  # Can modify their own invitations
        "delete": True,  # Can delete their own invitations
    },
    "amenities": {
        "view": True,
        "create": False,
        "modify": False,
        "delete": False,
    },
}
