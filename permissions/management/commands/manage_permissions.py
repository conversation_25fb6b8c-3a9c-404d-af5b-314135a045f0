import uuid

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction

from departments.models import Department
from permissions.models import DepartmentPermission, PermissionType


class Command(BaseCommand):
    help = "Manage permissions in the system"

    def add_arguments(self, parser):
        parser.add_argument(
            "action", type=str, help="Action to perform (list, add, remove, assign)"
        )
        parser.add_argument(
            "--permission", type=str, help="Permission code (e.g., organization)"
        )
        parser.add_argument("--name", type=str, help="Permission name")
        parser.add_argument("--description", type=str, help="Permission description")
        parser.add_argument("--category", type=str, help="Permission category")
        parser.add_argument("--department", type=str, help="Department ID")
        parser.add_argument(
            "--actions",
            type=str,
            help="Comma-separated list of actions (view,create,update,delete,manage)",
        )

    def handle(self, *args, **options):
        action = options["action"].lower()

        if action == "list":
            self.list_permissions()
        elif action == "add":
            self.add_permission(options)
        elif action == "remove":
            self.remove_permission(options)
        elif action == "assign":
            self.assign_permission(options)
        else:
            raise CommandError(f"Unknown action: {action}")

    def list_permissions(self):
        """List all permission types in the system"""
        permissions = PermissionType.objects.all().order_by("category", "name")

        if not permissions:
            self.stdout.write(self.style.WARNING("No permissions found in the system"))
            return

        self.stdout.write(self.style.SUCCESS("Available permissions:"))
        for perm in permissions:
            self.stdout.write(f"\n{perm.name} ({perm.code})")
            self.stdout.write(f"Category: {perm.category}")
            self.stdout.write(f"Description: {perm.description}")

            # List departments with this permission
            dept_perms = DepartmentPermission.objects.filter(permission_type=perm)
            if dept_perms:
                self.stdout.write("\nAssigned to departments:")
                for dept_perm in dept_perms:
                    actions = []
                    if dept_perm.can_view:
                        actions.append("view")
                    if dept_perm.can_create:
                        actions.append("create")
                    if dept_perm.can_update:
                        actions.append("update")
                    if dept_perm.can_delete:
                        actions.append("delete")
                    if dept_perm.can_manage:
                        actions.append("manage")

                    self.stdout.write(
                        f"- {dept_perm.department.name}: {', '.join(actions)}"
                    )

    def add_permission(self, options):
        """Add a new permission type"""
        required_fields = ["permission", "name", "description", "category"]
        for field in required_fields:
            if not options.get(field):
                raise CommandError(f"Missing required field: {field}")

        try:
            with transaction.atomic():
                permission = PermissionType.objects.create(
                    id=uuid.uuid4(),
                    name=options["name"],
                    code=options["permission"],
                    description=options["description"],
                    category=options["category"],
                )
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Successfully created permission: {permission.name}"
                    )
                )
        except Exception as e:
            raise CommandError(f"Failed to create permission: {str(e)}")

    def remove_permission(self, options):
        """Remove a permission type"""
        if not options.get("permission"):
            raise CommandError("Permission code is required")

        try:
            permission = PermissionType.objects.get(code=options["permission"])
            permission.delete()
            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully removed permission: {permission.name}"
                )
            )
        except PermissionType.DoesNotExist:
            raise CommandError(f'Permission not found: {options["permission"]}')
        except Exception as e:
            raise CommandError(f"Failed to remove permission: {str(e)}")

    def assign_permission(self, options):
        """Assign a permission to a department with specific actions"""
        required_fields = ["permission", "department", "actions"]
        for field in required_fields:
            if not options.get(field):
                raise CommandError(f"Missing required field: {field}")

        try:
            permission = PermissionType.objects.get(code=options["permission"])
            department = Department.objects.get(id=options["department"])

            # Parse actions
            actions = options["actions"].lower().split(",")
            valid_actions = ["view", "create", "update", "delete", "manage"]
            for action in actions:
                if action not in valid_actions:
                    raise CommandError(f"Invalid action: {action}")

            with transaction.atomic():
                dept_perm, created = DepartmentPermission.objects.get_or_create(
                    department=department,
                    permission_type=permission,
                    defaults={
                        "can_view": "view" in actions,
                        "can_create": "create" in actions,
                        "can_update": "update" in actions,
                        "can_delete": "delete" in actions,
                        "can_manage": "manage" in actions,
                    },
                )

                if not created:
                    dept_perm.can_view = "view" in actions
                    dept_perm.can_create = "create" in actions
                    dept_perm.can_update = "update" in actions
                    dept_perm.can_delete = "delete" in actions
                    dept_perm.can_manage = "manage" in actions
                    dept_perm.save()

                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully {"created" if created else "updated"} permission assignment: '
                        f"{permission.name} -> {department.name}"
                    )
                )

        except PermissionType.DoesNotExist:
            raise CommandError(f'Permission not found: {options["permission"]}')
        except Department.DoesNotExist:
            raise CommandError(f'Department not found: {options["department"]}')
        except Exception as e:
            raise CommandError(f"Failed to assign permission: {str(e)}")
