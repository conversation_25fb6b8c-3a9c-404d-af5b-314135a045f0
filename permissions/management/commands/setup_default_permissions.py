from django.core.management.base import BaseCommand
from django.db import transaction
from permissions.models import PermissionType
import uuid

DEFAULT_PERMISSIONS = [
    {
        'name': 'Organization Management',
        'code': 'organization',
        'description': 'Manage organization settings and configuration',
        'category': 'organization'
    },
    {
        'name': 'Department Management',
        'code': 'department',
        'description': 'Manage departments and their settings',
        'category': 'department'
    },
    {
        'name': 'User Management',
        'code': 'user',
        'description': 'Manage users and their roles',
        'category': 'user'
    },
    {
        'name': 'Booking Management',
        'code': 'booking',
        'description': 'Manage bookings and their settings',
        'category': 'booking'
    },
    {
        'name': 'Space Management',
        'code': 'space',
        'description': 'Manage spaces and their settings',
        'category': 'space'
    },
    {
        'name': 'Location Management',
        'code': 'location',
        'description': 'Manage locations and their settings',
        'category': 'location'
    },
]

class Command(BaseCommand):
    help = 'Sets up default permissions in the system'

    def handle(self, *args, **options):
        self.stdout.write('Setting up default permissions...')
        
        with transaction.atomic():
            for perm_data in DEFAULT_PERMISSIONS:
                permission, created = PermissionType.objects.get_or_create(
                    code=perm_data['code'],
                    defaults={
                        'id': uuid.uuid4(),
                        'name': perm_data['name'],
                        'description': perm_data['description'],
                        'category': perm_data['category']
                    }
                )
                
                if created:
                    self.stdout.write(self.style.SUCCESS(f'Created permission: {permission.name}'))
                else:
                    self.stdout.write(self.style.WARNING(f'Permission already exists: {permission.name}'))

        self.stdout.write(self.style.SUCCESS('Successfully set up default permissions'))
