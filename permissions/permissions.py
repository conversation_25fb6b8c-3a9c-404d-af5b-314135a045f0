from rest_framework.exceptions import NotFound, PermissionDenied
from rest_framework.permissions import BasePermission

from jechspace_backend.utils import ErrorCodes
from users.models import Roles

from .constants import MEMBER_PERMISSIONS


class IsSuperuser(BasePermission):
    """
    Permission class to restrict access to superusers only.

    Usage:
        class MySuperuserOnlyView(APIView):
            permission_classes = [IsSuperuser]
    """

    def has_permission(self, request, view):
        return bool(request.user and request.user.is_superuser)


class HasResourcePermission(BasePermission):
    """
    Base permission class that follows standard hierarchy:
    1. Check organization membership
    2. Fast-track for admins/owners
    3. Check department permissions for employees
    4. Check member permissions for regular members

    Also caches user, organization and department objects for reuse in the view.

    Usage:
        class MyView(APIView):
            permission_classes = [HasResourcePermission]
            permission_code = PermissionCategories.BOOKING
            permission_action = 'view'

            def get(self, request, *args, **kwargs):
                # Access cached objects
                organization = request.organization
                user_org = request.user_org_relation
                department = request.department  # May be None
    """

    def has_permission(self, request, view):
        # Fast-track for superusers
        if hasattr(request.user, "is_superuser") and request.user.is_superuser:
            return True

        # Extract permission requirements
        permission_code = getattr(view, "permission_code", None)
        permission_action = getattr(view, "permission_action", None)

        # Prevent access if permission requirements aren't defined
        if not permission_code or not permission_action:
            raise PermissionDenied(
                detail="Permission code or action not defined",
                code=ErrorCodes.PERMISSION_CONFIG_MISSING,
            )

        # Extract organization_id from URL kwargs
        organization_id = view.kwargs.get("organization_id")

        if not organization_id:
            organization_id = request.query_params.get("organization_id")

        # If no organization context is found, only allow superusers
        if not organization_id:
            # Allow all authenticated users for get methods if configured
            skip_org_for_get = getattr(view, "allow_without_org", False)
            if skip_org_for_get and request.method.lower() == "get":
                return True
            raise NotFound(
                detail="Organization not found", code=ErrorCodes.ORGANIZATION_NOT_FOUND
            )

        # Check organization membership
        user_org = request.user.organizations.filter(
            organization_id=organization_id
        ).first()
        if not user_org:
            raise PermissionDenied(
                detail="User is not a member of this organization",
                code=ErrorCodes.USER_NOT_IN_ORGANIZATION,
            )

        # Store important objects for reuse in the view
        request.user_org_relation = user_org
        request.organization = user_org.organization
        request.department = user_org.department

        # Fast-track for admins/owners
        if user_org.role in [Roles.ADMIN, Roles.OWNER]:
            return True

        # For employees, check department permissions
        if user_org.role == Roles.EMPLOYEE and user_org.department:
            return self._check_department_permission(request, view, user_org.department)

        # For members, check member permissions
        if user_org.role == Roles.MEMBER:
            return self._check_member_permission(request, view)

        raise PermissionDenied(
            detail="User does not have sufficient permissions",
            code=ErrorCodes.INSUFFICIENT_PERMISSIONS,
        )

    def _check_department_permission(self, request, view, department):
        """Check department-specific permissions"""
        permission_code = getattr(view, "permission_code", None)
        permission_action = getattr(view, "permission_action", None)

        if not permission_code or not permission_action:
            raise PermissionDenied(
                detail="Permission code or action not defined",
                code=ErrorCodes.PERMISSION_CONFIG_MISSING,
            )

        try:
            from .models import DepartmentPermission

            permission = DepartmentPermission.objects.get(
                department=department, permission_type__code=permission_code
            )
            if not getattr(permission, f"can_{permission_action}", False):
                raise PermissionDenied(
                    detail=f"Department does not have '{permission_action}' permission for '{permission_code}'",
                    code=ErrorCodes.DEPARTMENT_PERMISSION_DENIED,
                )
            return True
        except DepartmentPermission.DoesNotExist:
            raise PermissionDenied(
                detail=f"Department does not have any permissions for '{permission_code}'",
                code=ErrorCodes.DEPARTMENT_PERMISSION_NOT_FOUND,
            )

    def _check_member_permission(self, request, view):
        """Check member hardcoded permissions"""
        permission_code = getattr(view, "permission_code", None)
        permission_action = getattr(view, "permission_action", None)

        if not permission_code or not permission_action:
            raise PermissionDenied(
                detail="Permission code or action not defined",
                code=ErrorCodes.PERMISSION_CONFIG_MISSING,
            )

        has_permission = MEMBER_PERMISSIONS.get(permission_code, {}).get(
            permission_action, False
        )
        if not has_permission:
            raise PermissionDenied(
                detail=f"Members do not have '{permission_action}' permission for '{permission_code}'",
                code=ErrorCodes.MEMBER_PERMISSION_DENIED,
            )
        return True

    def has_object_permission(self, request, view, obj):
        # First ensure basic permission is granted
        if not self.has_permission(request, view):
            return False

        # For admins/owners, allow any object access
        if hasattr(request, "user_org_relation") and request.user_org_relation.role in [
            Roles.ADMIN,
            Roles.OWNER,
        ]:
            return True

        # For members and employees with ownership requirements:
        permission_action = getattr(view, "permission_action", None)
        if permission_action in ["modify", "delete", "update"]:
            # Check object ownership based on common patterns
            if hasattr(obj, "user_id") and obj.user_id == request.user.id:
                return True
            if hasattr(obj, "created_by_id") and obj.created_by_id == request.user.id:
                return True
            raise PermissionDenied(
                detail="User does not have permission to modify this object",
                code=ErrorCodes.OBJECT_PERMISSION_DENIED,
            )

        # For members doing only 'view' actions, may still need access control
        # Default to parent permission check
        return True
