from rest_framework import serializers

from departments.models import Department

from .models import DepartmentPermission, PermissionType


class PermissionTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for the PermissionType model
    """

    class Meta:
        model = PermissionType
        fields = [
            "id",
            "name",
            "description",
            "code",
            "category",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]


class DepartmentPermissionSerializer(serializers.ModelSerializer):
    """
    Serializer for the DepartmentPermission model
    """

    permission_type_id = serializers.UUIDField(required=True)

    class Meta:
        model = DepartmentPermission
        fields = [
            "id",
            "department_id",
            "permission_type_id",
            "can_view",
            "can_create",
            "can_update",
            "can_delete",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def validate(self, data):
        department_id = data.get("department_id")
        permission_type_id = data.get("permission_type_id")

        # Check if this permission already exists
        if DepartmentPermission.objects.filter(
            department_id=department_id, permission_type_id=permission_type_id
        ).exists():
            raise serializers.ValidationError(
                "This permission is already assigned to this department.",
                code="permission_already_assigned",
            )

        return data

    def create(self, validated_data):
        permission_type_id = validated_data.pop("permission_type_id")

        try:
            department = self.context["department"]
            permission_type = PermissionType.objects.get(id=permission_type_id)
        except (Department.DoesNotExist, PermissionType.DoesNotExist):
            raise serializers.ValidationError(
                "Department or permission type not found.", code="not_found"
            )

        validated_data["department"] = department
        validated_data["permission_type"] = permission_type
        validated_data.setdefault("can_view", False)
        validated_data.setdefault("can_create", False)
        validated_data.setdefault("can_update", False)
        validated_data.setdefault("can_delete", False)

        print(validated_data)

        return super().create(validated_data)
