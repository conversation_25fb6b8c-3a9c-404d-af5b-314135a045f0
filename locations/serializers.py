import json

from django.core.exceptions import ObjectDoesNotExist
from django.db import transaction

from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from amenities.models import Amenity
from amenities.serializers import AmenitySerializer
from jechspace_backend.utils import ErrorCodes
from users.models import User
from core.serializers import ContactSerializer

from .error_codes import LocationErrorCodes
from .models import Location, LocationBookingPolicy
from .validators import BookingPolicyValidator


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ["id", "email", "first_name", "last_name"]


class LocationBookingPolicySerializer(serializers.ModelSerializer):
    class Meta:
        model = LocationBookingPolicy
        fields = [
            "min_duration_minutes",
            "max_duration_minutes",
            "booking_interval",
            "cancel_before_minutes",
            "advance_booking_minutes",
            "requires_approval",
            "requires_approval_for_changes",
            "version",
        ]
        read_only_fields = ["id", "version"]

    def validate(self, data):
        """
        Validates booking policy constraints.
        """
        # Validate duration constraints
        min_duration = data.get("min_duration_minutes")
        max_duration = data.get("max_duration_minutes")

        if min_duration and max_duration:
            BookingPolicyValidator.validate_duration(min_duration, max_duration)

        # Validate other constraints
        if "booking_interval" in data:
            BookingPolicyValidator.validate_interval(data["booking_interval"])
        if "advance_booking_minutes" in data:
            BookingPolicyValidator.validate_advance_booking(
                data["advance_booking_minutes"]
            )
        if "cancel_before_minutes" in data:
            BookingPolicyValidator.validate_cancel_time(data["cancel_before_minutes"])

        return data

    def update(self, instance, validated_data):
        # Check for concurrent updates
        version = validated_data.pop("version", None)
        if version and instance.version != version:
            raise ValidationError(
                LocationErrorCodes.CONCURRENT_UPDATE.message,
                code=LocationErrorCodes.CONCURRENT_UPDATE.code,
            )

        # Update version
        validated_data["version"] = instance.version + 1

        return super().update(instance, validated_data)


class LocationAddressSerializer(serializers.Serializer):
    address = serializers.CharField(max_length=355)
    city = serializers.CharField(max_length=100)
    state = serializers.CharField(max_length=100)
    postal_code = serializers.CharField(max_length=20)
    country = serializers.CharField(max_length=100)


class LocationContactSerializer(ContactSerializer):
    pass


class LocationGeocodingSerializer(serializers.Serializer):
    latitude = serializers.DecimalField(max_digits=9, decimal_places=6, required=False)
    longitude = serializers.DecimalField(max_digits=9, decimal_places=6, required=False)


class LocationListSerializer(serializers.ModelSerializer):
    version = serializers.IntegerField(read_only=True)

    amenities = AmenitySerializer(many=True, read_only=True)
    stats = serializers.SerializerMethodField()

    # Add nested serializers for address and contact
    address = LocationAddressSerializer(source="*", read_only=True)
    contact = LocationContactSerializer(source="*", read_only=True)
    geocoding_fields = LocationGeocodingSerializer(source="*", read_only=True)

    class Meta:
        model = Location
        fields = [
            "id",
            "name",
            "address",
            "contact",
            "timezone",
            "capacity",
            "description",
            "amenities",
            "images",
            "status",
            "geocoding_fields",
            "stats",
            "created_at",
            "updated_at",
            "version",
            "total_spaces",
            "available_spaces",
        ]

    def get_stats(self, obj):
        return {
            "total_spaces": obj.total_spaces,
            "available_spaces": obj.available_spaces,
        }


class LocationCreateSerializer(serializers.ModelSerializer):
    amenity_ids = serializers.ListField(
        child=serializers.UUIDField(), required=False, write_only=True
    )
    booking_policy = serializers.JSONField()
    address = serializers.JSONField()
    contact = serializers.JSONField()
    geocoding_fields = serializers.JSONField(required=False)

    class Meta:
        model = Location
        fields = [
            "name",
            "address",
            "timezone",
            "capacity",
            "description",
            "amenity_ids",
            "opening_hours",
            "images",
            "map",
            "status",
            "booking_policy",
            "contact",
            "geocoding_fields",
        ]

    def validate(self, data):
        # Validate amenity IDs
        amenity_ids = data.get("amenity_ids", [])
        if amenity_ids:
            existing_amenities = set(
                Amenity.objects.filter(id__in=amenity_ids).values_list("id", flat=True)
            )

            invalid_amenities = set(amenity_ids) - existing_amenities
            if invalid_amenities:
                raise ValidationError(
                    {"amenity_ids": LocationErrorCodes.INVALID_AMENITY.message},
                    code=LocationErrorCodes.INVALID_AMENITY.code,
                )

        # Validate nested JSON fields
        try:
            # Validate address
            address_serializer = LocationAddressSerializer(data=data["address"])
            address_serializer.is_valid(raise_exception=True)
            data["address"] = address_serializer.validated_data

            # Validate contact
            contact_serializer = LocationContactSerializer(data=data["contact"])
            contact_serializer.is_valid(raise_exception=True)
            data["contact"] = contact_serializer.validated_data

            # Validate booking policy
            booking_policy_serializer = LocationBookingPolicySerializer(
                data=data["booking_policy"]
            )
            booking_policy_serializer.is_valid(raise_exception=True)
            data["booking_policy"] = booking_policy_serializer.validated_data

            # Validate geocoding fields if provided
            if "geocoding_fields" in data:
                geocoding_serializer = LocationGeocodingSerializer(
                    data=data["geocoding_fields"]
                )
                geocoding_serializer.is_valid(raise_exception=True)
                data["geocoding_fields"] = geocoding_serializer.validated_data

        except serializers.ValidationError as e:
            raise serializers.ValidationError(e.detail)
        except json.JSONDecodeError:
            raise serializers.ValidationError("Invalid JSON format in nested fields")

        return data

    @transaction.atomic
    def create(self, validated_data):
        # Extract nested objects
        amenity_ids = validated_data.pop("amenity_ids", [])
        booking_policy_data = validated_data.pop("booking_policy", {})
        address_data = validated_data.pop("address", {})
        contact_data = validated_data.pop("contact", {})
        geocoding_data = validated_data.pop("geocoding_fields", {})

        # Update location data with address and contact fields
        validated_data.update(address_data)
        validated_data.update(contact_data)

        # Update location data with geocoding fields if provided
        if geocoding_data:
            validated_data.update(geocoding_data)

        # Create location
        user = self.context["request"].user
        organization = self.context["request"].organization

        location = Location.objects.create(
            **validated_data, organization=organization, created_by=user
        )

        # Add amenities
        if amenity_ids:
            amenities = Amenity.objects.filter(id__in=amenity_ids)
            location.amenities.add(*amenities)

        # Create booking policy
        LocationBookingPolicy.objects.create(location=location, **booking_policy_data)

        return location


class LocationDetailSerializer(serializers.ModelSerializer):
    amenities = AmenitySerializer(many=True, read_only=True)
    booking_policy = LocationBookingPolicySerializer(read_only=True)
    created_by = UserSerializer(read_only=True)
    stats = serializers.SerializerMethodField()
    version = serializers.IntegerField(read_only=True)

    # Add nested serializers for address and contact
    address = LocationAddressSerializer(source="*", read_only=True)
    contact = LocationContactSerializer(source="*", read_only=True)
    geocoding_fields = LocationGeocodingSerializer(source="*", read_only=True)

    class Meta:
        model = Location
        fields = [
            "id",
            "name",
            "address",
            "contact",
            "timezone",
            "capacity",
            "description",
            "amenities",
            "opening_hours",
            "images",
            "status",
            "geocoding_fields",
            "booking_policy",
            "stats",
            "created_at",
            "updated_at",
            "created_by",
            "version",
        ]

    def get_stats(self, obj):
        return {
            "total_spaces": obj.total_spaces,
            "available_spaces": obj.available_spaces,
        }


class LocationUpdateSerializer(serializers.ModelSerializer):
    version = serializers.IntegerField(read_only=True)  # For optimistic locking

    class Meta:
        model = Location
        fields = [
            "name",
            "address",
            "city",
            "state",
            "country",
            "postal_code",
            "timezone",
            "capacity",
            "description",
            "opening_hours",
            "images",
            "map",
            "status",
            "longitude",
            "latitude",
            "version",
        ]
        extra_kwargs = {
            field: {"required": False} for field in fields if field != "version"
        }

    def validate(self, data):
        # Check for concurrent updates
        version = self.context.get("version")
        if version and self.instance.version != version:
            raise ValidationError(
                LocationErrorCodes.CONCURRENT_UPDATE.message,
                code=LocationErrorCodes.CONCURRENT_UPDATE.code,
            )
        return data

    def update(self, instance, validated_data):
        # Update version
        validated_data["version"] = instance.version + 1
        return super().update(instance, validated_data)


class LocationBookingPolicyUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = LocationBookingPolicy
        fields = [
            "min_duration_minutes",
            "max_duration_minutes",
            "booking_interval",
            "advance_booking_minutes",
            "cancel_before_minutes",
            "requires_approval",
            "requires_approval_for_changes",
        ]
        # All fields are optional for PATCH
        extra_kwargs = {field: {"required": False} for field in fields}


class LocationAmenityActionSerializer(serializers.Serializer):
    amenity_id = serializers.UUIDField()
    action = serializers.ChoiceField(choices=["add", "remove"])
    version = serializers.IntegerField(read_only=True)  # For optimistic locking

    def validate_amenity_id(self, value):
        try:
            Amenity.objects.get(id=value)
            return value
        except ObjectDoesNotExist:
            raise ValidationError(
                LocationErrorCodes.INVALID_AMENITY.message,
                code=LocationErrorCodes.INVALID_AMENITY.code,
            )
