from django.core.exceptions import ValidationError
from django.db.models import Q

from drf_spectacular.types import OpenApiTypes
from drf_spectacular.utils import OpenApiParameter, OpenApiResponse, extend_schema
from rest_framework import status, viewsets
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>, MultiPartParser

from core.pagination import CustomPageNumberPagination
from jechspace_backend.utils import api_response
from organizations.mixins import OrganizationPermissionMixin
from permissions.constants import PermissionActions, PermissionCategories

from .error_codes import LocationErrorCodes
from .models import Location
from .serializers import (
    LocationAmenityActionSerializer,
    LocationBookingPolicyUpdateSerializer,
    LocationCreateSerializer,
    LocationDetailSerializer,
    LocationListSerializer,
    LocationUpdateSerializer,
)


class LocationViewSet(OrganizationPermissionMixin, viewsets.GenericViewSet):
    """
    API endpoint for managing locations.

    Provides endpoints for creating, retrieving, updating, and deleting locations.
    Implements optimistic locking for concurrent update handling.

    Features:
    - Full CRUD operations with proper validation
    - Optimistic locking for concurrent updates
    - Filtering by status, city, country
    - Search by name, description, address
    - Pagination with custom ordering
    - Proper permission handling
    """

    permission_category = PermissionCategories.LOCATION
    permission_action = PermissionActions.VIEW
    pagination_class = CustomPageNumberPagination
    parser_classes = (MultiPartParser, FormParser)
    ordering_fields = ["name", "created_at", "status", "city", "country"]
    ordering = ["-name"]

    def get_queryset(self):
        organization_id = self.kwargs.get("organization_id")
        queryset = Location.objects.filter(organization_id=organization_id)

        # Apply filters
        status_filter = self.request.query_params.get("status")
        search_query = self.request.query_params.get("search")
        city_filter = self.request.query_params.get("city")
        country_filter = self.request.query_params.get("country")

        if status_filter:
            queryset = queryset.filter(status=status_filter)
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query)
                | Q(description__icontains=search_query)
                | Q(address__icontains=search_query)
            )
        if city_filter:
            queryset = queryset.filter(city__iexact=city_filter)
        if country_filter:
            queryset = queryset.filter(country__iexact=country_filter)

        return queryset.select_related("booking_policy", "created_by").prefetch_related(
            "amenities"
        )

    def get_serializer_class(self):
        if self.action == "create":
            return LocationCreateSerializer
        elif self.action == "list":
            return LocationListSerializer
        elif self.action in ["update", "partial_update"]:
            return LocationUpdateSerializer
        return LocationDetailSerializer

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context["organization_id"] = self.kwargs.get("organization_id")
        if not getattr(self, "swagger_fake_view", False):
            context["organization"] = self.request.organization
        if self.action in ["update", "partial_update"]:
            context["version"] = self.request.data.get("version")
        return context

    @extend_schema(
        summary="Create a new Location",
        description="""
        Create a new location within an organization.
        Requires admin permissions.

        Features:
        - Validates all input data
        - Handles file uploads (images and map)
        - Creates associated booking policy
        - Associates amenities
        - Sanitizes text input
        - Validates timezone and opening hours
        """,
        request=LocationCreateSerializer,
        responses={
            201: OpenApiResponse(
                response=LocationDetailSerializer,
                description="Location created successfully",
            ),
            400: OpenApiResponse(
                description="Bad Request - Validation Error",
                response=OpenApiTypes.OBJECT,
            ),
            403: OpenApiResponse(description="Forbidden - Not allowed"),
        },
        tags=["Locations"],
    )
    def create(self, request, *args, **kwargs):
        """Create a new location with proper validation and error handling."""
        self.permission_action = PermissionActions.CREATE
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            location = serializer.save()
            detail_serializer = LocationDetailSerializer(location)
            return api_response(
                message="Location created successfully",
                data=detail_serializer.data,
                status_code=status.HTTP_201_CREATED,
            )
        except ValidationError as e:
            return api_response(
                message=str(e), status_code=status.HTTP_400_BAD_REQUEST, errors=e
            )

    @extend_schema(
        summary="List Organization Locations",
        description="""
        List all locations for an organization with filtering, pagination, and ordering.

        Features:
        - Filter by status, city, country
        - Search by name, description, address
        - Pagination with customizable page size
        - Custom ordering by name, created_at, status, city, country
        - Optimistic locking version included
        """,
        parameters=[
            OpenApiParameter(
                name="status",
                type=str,
                enum=["active", "inactive", "under_maintenance"],
                description="Filter by location status",
            ),
            OpenApiParameter(
                name="search",
                type=str,
                description="Search in name, description, and address",
            ),
            OpenApiParameter(name="city", type=str, description="Filter by city"),
            OpenApiParameter(name="country", type=str, description="Filter by country"),
            OpenApiParameter(
                name="ordering",
                type=str,
                description="Order by field (allowed: name, created_at, status, city, country). Prefix with - for descending order",
                examples=["name", "-created_at", "status"],
            ),
        ],
        responses={
            200: OpenApiResponse(
                response=LocationListSerializer(many=True),
                description="List of locations with pagination metadata",
            ),
            403: OpenApiResponse(description="Forbidden - Not allowed"),
        },
        tags=["Locations"],
    )
    def list(self, request, *args, **kwargs):
        """List all locations with proper filtering, pagination, and ordering."""
        self.permission_action = PermissionActions.VIEW
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginator.paginate_queryset(queryset, request, self)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.paginator.get_paginated_response(
                serializer.data, message="Locations retrieved successfully"
            )

        serializer = self.get_serializer(queryset, many=True)
        return api_response(
            message="Locations retrieved successfully",
            data={"items": serializer.data, "pagination": None},
        )

    @extend_schema(
        summary="Get Location Details",
        description="""
        Get detailed information about a specific location.

        Features:
        - Includes all location details
        - Includes booking policy
        - Includes amenities
        - Includes statistics
        - Optimistic locking version included
        """,
        responses={
            200: OpenApiResponse(
                response=LocationDetailSerializer, description="Location details"
            ),
            404: OpenApiResponse(description="Location not found"),
            403: OpenApiResponse(description="Forbidden - Not allowed"),
        },
        tags=["Locations"],
    )
    def retrieve(self, request, *args, **kwargs):
        """Get detailed information about a specific location."""
        self.permission_action = PermissionActions.VIEW

        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return api_response(
            message="Location details retrieved successfully", data=serializer.data
        )

    @extend_schema(
        summary="Update Location",
        description="""
        Update a location's details.
        Requires admin permissions.

        Features:
        - Partial updates supported
        - Optimistic locking for concurrent updates
        - Validates all input data
        - Handles file uploads
        - Sanitizes text input
        """,
        request=LocationUpdateSerializer,
        responses={
            200: OpenApiResponse(
                response=LocationDetailSerializer,
                description="Location updated successfully",
            ),
            400: OpenApiResponse(description="Bad Request - Validation Error"),
            403: OpenApiResponse(description="Forbidden - Not allowed"),
            409: OpenApiResponse(description="Conflict - Concurrent update detected"),
        },
        tags=["Locations"],
    )
    def update(self, request, *args, **kwargs):
        """Update a location with proper validation and concurrent update handling."""
        self.permission_action = PermissionActions.UPDATE
        partial = kwargs.pop("partial", False)
        instance = self.get_object()

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        try:
            location = serializer.save()
            detail_serializer = LocationDetailSerializer(location)
            return api_response(
                message="Location updated successfully", data=detail_serializer.data
            )
        except ValidationError as e:
            if e.code == LocationErrorCodes.CONCURRENT_UPDATE.code:
                return api_response(
                    message=str(e),
                    status_code=status.HTTP_409_CONFLICT,
                    error_code=e.code,
                )
            return api_response(
                message=str(e),
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=e.code if hasattr(e, "code") else None,
            )

    @extend_schema(
        summary="Delete Location",
        description="""
        Delete a location.
        Requires admin permissions.

        Features:
        - Checks for active bookings
        - Handles cascading deletes
        - Validates organization ownership
        """,
        responses={
            200: OpenApiResponse(description="Location deleted successfully"),
            403: OpenApiResponse(description="Forbidden - Not allowed"),
            404: OpenApiResponse(description="Location not found"),
            400: OpenApiResponse(
                description="Bad Request - Location has active bookings"
            ),
        },
        tags=["Locations"],
    )
    def destroy(self, request, *args, **kwargs):
        """Delete a location with proper validation."""
        self.permission_action = PermissionActions.DELETE
        instance = self.get_object()

        # Check for active bookings
        if (
            hasattr(instance, "spaces")
            and instance.spaces.filter(bookings__status="active").exists()
        ):
            return api_response(
                message=LocationErrorCodes.LOCATION_IN_USE.message,
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=LocationErrorCodes.LOCATION_IN_USE.code,
            )

        instance.delete()
        return api_response(message="Location deleted successfully", data=None)


class LocationBookingPolicyViewSet(
    OrganizationPermissionMixin, viewsets.GenericViewSet
):
    """
    API endpoint for managing location booking policies.
    Implements optimistic locking for concurrent update handling.
    """

    permission_category = PermissionCategories.LOCATION
    permission_action = PermissionActions.UPDATE
    serializer_class = LocationBookingPolicyUpdateSerializer

    def get_object(self):
        location = Location.objects.get(
            id=self.kwargs.get("location_id"),
            organization_id=self.kwargs.get("organization_id"),
        )
        return location.booking_policy

    @extend_schema(
        summary="Update Location Booking Policy",
        description="""
        Update a location's booking policy.
        Requires admin permissions.

        Features:
        - Partial updates supported
        - Optimistic locking for concurrent updates
        - Validates all policy constraints
        """,
        request=LocationBookingPolicyUpdateSerializer,
        responses={
            200: OpenApiResponse(
                response=LocationBookingPolicyUpdateSerializer,
                description="Booking policy updated successfully",
            ),
            400: OpenApiResponse(description="Bad Request - Validation Error"),
            403: OpenApiResponse(description="Forbidden - Not allowed"),
            409: OpenApiResponse(description="Conflict - Concurrent update detected"),
        },
        tags=["Location Booking Policies"],
    )
    def update(self, request, *args, **kwargs):
        """Update a location's booking policy with proper validation."""
        partial = kwargs.pop("partial", False)
        instance = self.get_object()

        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)

        try:
            policy = serializer.save()
            return api_response(
                message="Booking policy updated successfully", data=serializer.data
            )
        except ValidationError as e:
            if e.code == LocationErrorCodes.CONCURRENT_UPDATE.code:
                return api_response(
                    message=str(e),
                    status_code=status.HTTP_409_CONFLICT,
                    error_code=e.code,
                )
            return api_response(
                message=str(e),
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=e.code if hasattr(e, "code") else None,
            )


class LocationAmenityViewSet(OrganizationPermissionMixin, viewsets.GenericViewSet):
    """
    API endpoint for managing location amenities.
    Implements optimistic locking for concurrent update handling.
    """

    permission_category = PermissionCategories.LOCATION
    permission_action = PermissionActions.UPDATE
    serializer_class = LocationAmenityActionSerializer

    def get_object(self):
        return Location.objects.get(
            id=self.kwargs.get("location_id"),
            organization_id=self.kwargs.get("organization_id"),
        )

    @extend_schema(
        summary="Manage Location Amenities",
        description="""
        Add or remove amenities from a location.
        Requires admin permissions.

        Features:
        - Add or remove amenities
        - Validates amenity existence
        - Optimistic locking for concurrent updates
        """,
        request=LocationAmenityActionSerializer,
        responses={
            200: OpenApiResponse(description="Amenity action completed successfully"),
            400: OpenApiResponse(description="Bad Request - Validation Error"),
            403: OpenApiResponse(description="Forbidden - Not allowed"),
            404: OpenApiResponse(description="Location or amenity not found"),
            409: OpenApiResponse(description="Conflict - Concurrent update detected"),
        },
        tags=["Location Amenities"],
    )
    def update(self, request, *args, **kwargs):
        """Add or remove amenities from a location."""
        location = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            amenity_id = serializer.validated_data["amenity_id"]
            action = serializer.validated_data["action"]

            if action == "add":
                location.amenities.add(amenity_id)
                message = "Amenity added to location successfully"
            else:
                location.amenities.remove(amenity_id)
                message = "Amenity removed from location successfully"

            # Update version for optimistic locking
            location.version += 1
            location.save()

            return api_response(message=message, data=None)
        except ValidationError as e:
            if e.code == LocationErrorCodes.CONCURRENT_UPDATE.code:
                return api_response(
                    message=str(e),
                    status_code=status.HTTP_409_CONFLICT,
                    error_code=e.code,
                )
            return api_response(
                message=str(e),
                status_code=status.HTTP_400_BAD_REQUEST,
                error_code=e.code if hasattr(e, "code") else None,
            )
