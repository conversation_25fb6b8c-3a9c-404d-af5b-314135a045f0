# Generated by Django 5.2 on 2025-06-03 12:56

import uuid

import django.core.validators
from django.db import migrations, models

import locations.validators


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("amenities", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="LocationBookingPolicy",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "min_duration_minutes",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)]
                    ),
                ),
                (
                    "max_duration_minutes",
                    models.PositiveIntegerField(
                        validators=[django.core.validators.MinValueValidator(1)]
                    ),
                ),
                (
                    "booking_interval",
                    models.PositiveIntegerField(
                        help_text="Booking interval in minutes between bookings",
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    "cancel_before_minutes",
                    models.PositiveIntegerField(
                        help_text="How many minutes before start time a booking can be canceled without penalty",
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "advance_booking_minutes",
                    models.PositiveIntegerField(
                        help_text="How far in advance bookings can be made",
                        validators=[django.core.validators.MinValueValidator(0)],
                    ),
                ),
                (
                    "requires_approval",
                    models.BooleanField(
                        default=False,
                        help_text="Determines if bookings in this location require approval",
                    ),
                ),
                (
                    "requires_approval_for_changes",
                    models.BooleanField(
                        default=False,
                        help_text="Determines if bookings in this location require approvals for changes",
                    ),
                ),
                ("version", models.PositiveIntegerField(default=1)),
            ],
            options={
                "abstract": False,
            },
        ),
        migrations.CreateModel(
            name="Location",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "name",
                    models.CharField(
                        max_length=255,
                        validators=[locations.validators.TextSanitizer()],
                    ),
                ),
                (
                    "address",
                    models.TextField(validators=[locations.validators.TextSanitizer()]),
                ),
                (
                    "city",
                    models.CharField(
                        max_length=255,
                        validators=[locations.validators.TextSanitizer()],
                    ),
                ),
                (
                    "state",
                    models.CharField(
                        max_length=255,
                        validators=[locations.validators.TextSanitizer()],
                    ),
                ),
                (
                    "country",
                    models.CharField(
                        max_length=255,
                        validators=[locations.validators.TextSanitizer()],
                    ),
                ),
                (
                    "postal_code",
                    models.CharField(
                        blank=True,
                        max_length=20,
                        null=True,
                        validators=[locations.validators.TextSanitizer()],
                    ),
                ),
                (
                    "timezone",
                    models.CharField(
                        max_length=100,
                        validators=[locations.validators.TimezoneValidator()],
                    ),
                ),
                (
                    "capacity",
                    models.PositiveIntegerField(
                        blank=True,
                        null=True,
                        validators=[django.core.validators.MinValueValidator(1)],
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        blank=True,
                        null=True,
                        validators=[locations.validators.TextSanitizer()],
                    ),
                ),
                (
                    "opening_hours",
                    models.JSONField(
                        validators=[locations.validators.OpeningHoursValidator()]
                    ),
                ),
                ("images", models.JSONField(blank=True, null=True)),
                (
                    "map",
                    models.ImageField(
                        blank=True,
                        null=True,
                        upload_to="locations/maps/",
                        validators=[locations.validators.ImageFileValidator()],
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("active", "Active"),
                            ("inactive", "Inactive"),
                            ("under_maintenance", "Under Maintenance"),
                        ],
                        default="active",
                        max_length=20,
                    ),
                ),
                (
                    "longitude",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=9, null=True
                    ),
                ),
                (
                    "latitude",
                    models.DecimalField(
                        blank=True, decimal_places=6, max_digits=9, null=True
                    ),
                ),
                ("version", models.PositiveIntegerField(default=1)),
                ("email", models.EmailField(max_length=254)),
                ("phone", models.CharField(max_length=20)),
                (
                    "amenities",
                    models.ManyToManyField(
                        blank=True, related_name="locations", to="amenities.amenity"
                    ),
                ),
            ],
        ),
    ]
