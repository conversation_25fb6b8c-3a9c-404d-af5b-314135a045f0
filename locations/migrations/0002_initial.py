# Generated by Django 5.2 on 2025-06-03 12:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("locations", "0001_initial"),
        ("organizations", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="location",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_locations",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="location",
            name="organization",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="locations",
                to="organizations.organization",
            ),
        ),
        migrations.AddField(
            model_name="locationbookingpolicy",
            name="location",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="booking_policy",
                to="locations.location",
            ),
        ),
        migrations.AddIndex(
            model_name="location",
            index=models.Index(
                fields=["organization", "city"], name="locations_l_organiz_597c75_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="location",
            index=models.Index(
                fields=["organization", "status"], name="locations_l_organiz_b80309_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="location",
            index=models.Index(
                fields=["organization", "name"], name="locations_l_organiz_856c45_idx"
            ),
        ),
        migrations.AddConstraint(
            model_name="location",
            constraint=models.UniqueConstraint(
                fields=("organization", "name"), name="unique_location_name_per_org"
            ),
        ),
    ]
