from enum import Enum


class LocationErrorCodes(Enum):
    # Validation Errors
    INVALID_TIMEZONE = (
        "INVALID_TIMEZONE",
        "Invalid timezone format. Must be a valid IANA timezone (e.g., 'America/New_York')",
    )
    INVALID_OPENING_HOURS = (
        "INVALID_OPENING_HOURS",
        "Invalid opening hours format. Must be a valid JSON with day keys and time ranges",
    )
    INVALID_IMAGE_FORMAT = (
        "INVALID_IMAGE_FORMAT",
        "Invalid image format. Only JPG, PNG, and WebP formats are allowed",
    )
    FILE_TOO_LARGE = ("FILE_TOO_LARGE", "File size exceeds the maximum limit of 5MB")
    INVALID_COORDINATES = (
        "INVALID_COORDINATES",
        "Invalid coordinates. Latitude must be between -90 and 90, longitude between -180 and 180",
    )
    INVALID_CAPACITY = (
        "INVALID_CAPACITY",
        "Invalid capacity. Must be a positive integer",
    )
    INVALID_STATUS = (
        "INVALID_STATUS",
        "Invalid status. Must be one of: active, inactive, under_maintenance",
    )

    # Booking Policy Errors
    INVALID_BOOKING_DURATION = (
        "INVALID_BOOKING_DURATION",
        "Invalid booking duration. Min duration must be less than max duration",
    )
    INVALID_BOOKING_INTERVAL = (
        "INVALID_BOOKING_INTERVAL",
        "Invalid booking interval. Must be a positive integer",
    )
    INVALID_ADVANCE_BOOKING = (
        "INVALID_ADVANCE_BOOKING",
        "Invalid advance booking time. Must be a non-negative integer",
    )
    INVALID_CANCEL_TIME = (
        "INVALID_CANCEL_TIME",
        "Invalid cancellation time. Must be a non-negative integer",
    )

    # Concurrent Update Errors
    CONCURRENT_UPDATE = (
        "CONCURRENT_UPDATE",
        "The location was modified by another user. Please refresh and try again",
    )

    # Business Logic Errors
    LOCATION_IN_USE = (
        "LOCATION_IN_USE",
        "Cannot delete location as it has active bookings",
    )
    DUPLICATE_LOCATION = (
        "DUPLICATE_LOCATION",
        "A location with this name already exists in the organization",
    )
    INVALID_AMENITY = (
        "INVALID_AMENITY",
        "One or more amenities are invalid or do not exist",
    )

    def __init__(self, code: str, message: str):
        self.code = code
        self.message = message

    @classmethod
    def get_error_response(
        cls, error_code: "LocationErrorCodes", details: dict = None
    ) -> dict:
        """Generate a standardized error response"""
        response = {
            "status": "error",
            "error": {"code": error_code.code, "message": error_code.message},
        }
        if details:
            response["error"]["details"] = details
        return response
