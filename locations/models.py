from django.contrib.auth import get_user_model
from django.core.validators import MinValueValidator
from django.db import models
from django.utils.translation import gettext_lazy as _

from amenities.models import Amenity
from core.models import BaseModel

from .validators import (
    BookingPolicyValidator,
    CoordinatesValidator,
    ImageFileValidator,
    OpeningHoursValidator,
    TextSanitizer,
    TimezoneValidator,
)

User = get_user_model()


class Location(BaseModel):
    """
    Location model - represents a physical location owned by an organization
    """

    class StatusChoices(models.TextChoices):
        ACTIVE = "active", _("Active")
        INACTIVE = "inactive", _("Inactive")
        UNDER_MAINTENANCE = "under_maintenance", _("Under Maintenance")

    organization = models.ForeignKey(
        "organizations.Organization", on_delete=models.CASCADE, related_name="locations"
    )
    name = models.CharField(max_length=255, validators=[TextSanitizer()])
    address = models.TextField(validators=[TextSanitizer()])
    city = models.CharField(max_length=255, validators=[TextSanitizer()])
    state = models.CharField(max_length=255, validators=[TextSanitizer()])
    country = models.CharField(max_length=255, validators=[TextSanitizer()])
    postal_code = models.CharField(
        max_length=20, blank=True, null=True, validators=[TextSanitizer()]
    )
    timezone = models.CharField(max_length=100, validators=[TimezoneValidator()])
    capacity = models.PositiveIntegerField(
        blank=True, null=True, validators=[MinValueValidator(1)]
    )
    description = models.TextField(blank=True, null=True, validators=[TextSanitizer()])
    amenities = models.ManyToManyField(Amenity, blank=True, related_name="locations")
    opening_hours = models.JSONField(validators=[OpeningHoursValidator()])
    images = models.JSONField(blank=True, null=True)
    map = models.ImageField(
        upload_to="locations/maps/",
        blank=True,
        null=True,
        validators=[ImageFileValidator()],
    )
    status = models.CharField(
        max_length=20, choices=StatusChoices.choices, default=StatusChoices.ACTIVE
    )
    longitude = models.DecimalField(
        max_digits=9, decimal_places=6, blank=True, null=True
    )
    latitude = models.DecimalField(
        max_digits=9, decimal_places=6, blank=True, null=True
    )
    created_by = models.ForeignKey(
        "users.User",
        on_delete=models.SET_NULL,
        related_name="created_locations",
        null=True,
    )
    version = models.PositiveIntegerField(default=1)  # For optimistic locking

    # Contact information
    email = models.EmailField()
    phone = models.CharField(max_length=20)

    def __str__(self):
        return f"{self.name} ({self.city}, {self.country}) - {self.organization.name}"

    def clean(self):
        super().clean()
        # Validate coordinates if both are provided
        if self.latitude is not None and self.longitude is not None:
            CoordinatesValidator()(self.latitude, "latitude")
            CoordinatesValidator()(self.longitude, "longitude")

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    @property
    def total_spaces(self):
        return self.spaces.count() if hasattr(self, "spaces") else 0

    @property
    def available_spaces(self):
        # Placeholder - implement based on your booking system
        # For example: return self.spaces.filter(is_available=True).count()
        return self.total_spaces // 2

    class Meta:
        indexes = [
            models.Index(fields=["organization", "city"]),
            models.Index(fields=["organization", "status"]),
            models.Index(fields=["organization", "name"]),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=["organization", "name"], name="unique_location_name_per_org"
            )
        ]


class LocationBookingPolicy(BaseModel):
    """
    LocationBookingPolicy model - represents booking rules for a location
    """

    location = models.OneToOneField(
        Location, on_delete=models.CASCADE, related_name="booking_policy"
    )
    min_duration_minutes = models.PositiveIntegerField(
        validators=[MinValueValidator(1)]
    )
    max_duration_minutes = models.PositiveIntegerField(
        validators=[MinValueValidator(1)]
    )
    booking_interval = models.PositiveIntegerField(
        validators=[MinValueValidator(1)],
        help_text="Booking interval in minutes between bookings",
    )
    cancel_before_minutes = models.PositiveIntegerField(
        validators=[MinValueValidator(0)],
        help_text="How many minutes before start time a booking can be canceled without penalty",
    )
    advance_booking_minutes = models.PositiveIntegerField(
        validators=[MinValueValidator(0)],
        help_text="How far in advance bookings can be made",
    )
    requires_approval = models.BooleanField(
        default=False,
        help_text="Determines if bookings in this location require approval",
    )
    requires_approval_for_changes = models.BooleanField(
        default=False,
        help_text="Determines if bookings in this location require approvals for changes",
    )
    version = models.PositiveIntegerField(default=1)  # For optimistic locking

    def __str__(self):
        return f"Booking Policy for {self.location.name}"

    def clean(self):
        super().clean()
        # Validate booking policy constraints
        BookingPolicyValidator.validate_duration(
            self.min_duration_minutes, self.max_duration_minutes
        )
        BookingPolicyValidator.validate_interval(self.booking_interval)
        BookingPolicyValidator.validate_advance_booking(self.advance_booking_minutes)
        BookingPolicyValidator.validate_cancel_time(self.cancel_before_minutes)

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)
