import re
from datetime import datetime
from typing import Any, Dict, List

from django.core.exceptions import ValidationError
from django.core.validators import MaxValueValidator, MinValueValidator
from django.utils.deconstruct import deconstructible

import pytz

from .error_codes import LocationErrorCodes


@deconstructible
class TimezoneValidator:
    """Validates that the timezone is a valid IANA timezone"""

    def __call__(self, value: str) -> None:
        try:
            pytz.timezone(value)
        except pytz.exceptions.UnknownTimeZoneError:
            raise ValidationError(
                LocationErrorCodes.INVALID_TIMEZONE.message,
                code=LocationErrorCodes.INVALID_TIMEZONE.code,
            )


@deconstructible
class OpeningHoursValidator:
    """Validates the opening hours format and time ranges"""

    DAYS_OF_WEEK = [
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
        "sunday",
    ]
    TIME_FORMAT = "%H:%M"

    def __call__(self, value):
        if not isinstance(value, dict):
            raise ValidationError(
                LocationErrorCodes.INVALID_OPENING_HOURS.message,
                code=LocationErrorCodes.INVALID_OPENING_HOURS.code,
            )

        for day, hours in value.items():
            if day not in self.DAYS_OF_WEEK:
                raise ValidationError(
                    f"Invalid day: {day}. Must be one of {', '.join(self.DAYS_OF_WEEK)}",
                    code=LocationErrorCodes.INVALID_OPENING_HOURS.code,
                )

            if hours is None:
                continue

            if not isinstance(hours, list):
                raise ValidationError(
                    f"Invalid hours format for {day}. Must be a list of time ranges",
                    code=LocationErrorCodes.INVALID_OPENING_HOURS.code,
                )

            for time_range in hours:
                if (
                    not isinstance(time_range, dict)
                    or "open" not in time_range
                    or "close" not in time_range
                ):
                    raise ValidationError(
                        f"Invalid time range format for {day}. Must be a dict with 'open' and 'close' keys",
                        code=LocationErrorCodes.INVALID_OPENING_HOURS.code,
                    )

                try:
                    open_time = datetime.strptime(time_range["open"], self.TIME_FORMAT)
                    close_time = datetime.strptime(
                        time_range["close"], self.TIME_FORMAT
                    )

                    if close_time <= open_time:
                        raise ValidationError(
                            f"Close time must be after open time for {day}",
                            code=LocationErrorCodes.INVALID_OPENING_HOURS.code,
                        )
                except ValueError:
                    raise ValidationError(
                        f"Invalid time format for {day}. Must be in HH:MM format",
                        code=LocationErrorCodes.INVALID_OPENING_HOURS.code,
                    )


@deconstructible
class ImageFileValidator:
    """Validates image file format and size"""

    ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".webp"}
    MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

    def __call__(self, value) -> None:
        if not value:
            return

        # Check file extension
        ext = value.name.lower()[-5:]  # Get last 5 chars to handle .jpeg
        if not any(ext.endswith(ext) for ext in self.ALLOWED_EXTENSIONS):
            raise ValidationError(
                LocationErrorCodes.INVALID_IMAGE_FORMAT.message,
                code=LocationErrorCodes.INVALID_IMAGE_FORMAT.code,
            )

        # Check file size
        if value.size > self.MAX_FILE_SIZE:
            raise ValidationError(
                LocationErrorCodes.FILE_TOO_LARGE.message,
                code=LocationErrorCodes.FILE_TOO_LARGE.code,
            )


@deconstructible
class CoordinatesValidator:
    """Validates latitude and longitude values"""

    def __call__(self, value, field_name):
        if value is None:
            return

        if field_name == "latitude" and not -90 <= value <= 90:
            raise ValidationError(
                LocationErrorCodes.INVALID_COORDINATES.message,
                code=LocationErrorCodes.INVALID_COORDINATES.code,
            )
        elif field_name == "longitude" and not -180 <= value <= 180:
            raise ValidationError(
                LocationErrorCodes.INVALID_COORDINATES.message,
                code=LocationErrorCodes.INVALID_COORDINATES.code,
            )


@deconstructible
class TextSanitizer:
    """Sanitizes text input to prevent XSS and other injection attacks"""

    def __call__(self, value):
        if not value:
            return value

        # Remove HTML tags
        value = re.sub(r"<[^>]+>", "", value)

        # Remove potentially dangerous characters
        value = re.sub(r"[<>{}[\]\\]", "", value)

        # Normalize whitespace
        value = " ".join(value.split())

        return value


class BookingPolicyValidator:
    """Validates booking policy constraints"""

    @staticmethod
    def validate_duration(min_duration: int, max_duration: int) -> None:
        if min_duration >= max_duration:
            raise ValidationError(
                LocationErrorCodes.INVALID_BOOKING_DURATION.message,
                code=LocationErrorCodes.INVALID_BOOKING_DURATION.code,
            )

    @staticmethod
    def validate_interval(interval: int) -> None:
        if interval <= 0:
            raise ValidationError(
                LocationErrorCodes.INVALID_BOOKING_INTERVAL.message,
                code=LocationErrorCodes.INVALID_BOOKING_INTERVAL.code,
            )

    @staticmethod
    def validate_advance_booking(minutes: int) -> None:
        if minutes < 0:
            raise ValidationError(
                LocationErrorCodes.INVALID_ADVANCE_BOOKING.message,
                code=LocationErrorCodes.INVALID_ADVANCE_BOOKING.code,
            )

    @staticmethod
    def validate_cancel_time(minutes: int) -> None:
        if minutes < 0:
            raise ValidationError(
                LocationErrorCodes.INVALID_CANCEL_TIME.message,
                code=LocationErrorCodes.INVALID_CANCEL_TIME.code,
            )
