from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView

from jechspace_backend.utils import ErrorCodes, api_response
from organizations.mixins import OrganizationPermissionMixin, OrganizationQueryMixin
from permissions.constants import PermissionActions, PermissionCategories
from spaces.models import Space
from users.models import Roles

from .models import Booking, BookingStatus
from .serializers import BookingSerializer
from .services import BookingValidationService


class BookingCreateListView(APIView):
    def post(self, request):
        """Create a new booking."""
        permission_classes = [IsAuthenticated]

        (
            organization,
            user,
            error_response,
        ) = OrganizationQueryMixin.get_organization_from_request(request)
        if error_response:
            return error_response

        serializer = BookingSerializer(
            data=request.data, context={"organization": organization, "user": user}
        )
        if serializer.is_valid():
            serializer.save(user=user)
            return api_response(
                data=serializer.data, message="Booking created successfully"
            )
        return api_response(
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
            errors=serializer.errors,
        )

    def get(self, request):
        """List all bookings with optional filtering."""
        permission_classes = [IsAuthenticated]

        (
            organization,
            user,
            error_response,
        ) = OrganizationQueryMixin.get_organization_from_request(request)
        if error_response:
            return error_response

        # Get query parameters for filtering
        space_slug = request.GET.get("space")
        status_filter = request.GET.get("status")
        start_date = request.GET.get("start_date")
        end_date = request.GET.get("end_date")

        # Start with all bookings for this organization
        queryset = Booking.objects.filter(space__location__organization=organization)

        # Apply filters
        if space_slug:
            try:
                space = Space.objects.get(
                    slug=space_slug, location__organization=organization
                )
                queryset = queryset.filter(space=space)
            except Space.DoesNotExist:
                return api_response(
                    message="Space with this slug does not exist.",
                    status_code=status.HTTP_404_NOT_FOUND,
                    success=False,
                    errors={
                        "space": {
                            "code": ErrorCodes.SPACE_NOT_FOUND,
                            "message": "Space not found",
                        }
                    },
                )

        # Filter by user (admin can see all, regular users only see their own)
        if user.role != Roles.ADMIN:
            queryset = queryset.filter(user=user)

        # Apply additional filters
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        if start_date:
            queryset = queryset.filter(start_time__gte=start_date)
        if end_date:
            queryset = queryset.filter(end_time__lte=end_date)

        # Order by most recent
        queryset = queryset.order_by("-created_at")

        serializer = BookingSerializer(queryset, many=True)
        return api_response(
            data=serializer.data, message="Bookings retrieved successfully"
        )


class BookingDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, booking_id):
        """Get a specific booking by ID."""
        (
            organization,
            user,
            error_response,
        ) = OrganizationQueryMixin.get_organization_from_request(request)
        if error_response:
            return error_response

        try:
            booking = Booking.objects.get(
                id=booking_id, space__location__organization=organization
            )

            # Only allow access if admin or booking owner
            if booking.user != user and user.role != Roles.ADMIN:
                return api_response(
                    message="You don't have permission to view this booking",
                    status_code=status.HTTP_403_FORBIDDEN,
                    success=False,
                    errors={
                        "permission": {
                            "code": ErrorCodes.PERMISSION_DENIED,
                            "message": "Access denied",
                        }
                    },
                )

            serializer = BookingSerializer(booking)
            return api_response(
                data=serializer.data, message="Booking details retrieved successfully"
            )
        except Booking.DoesNotExist:
            return api_response(
                message="Booking not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )


class CancelBookingView(APIView):
    permission_classes = [IsAuthenticated]

    def patch(self, request, booking_id):
        """Cancel a booking."""
        (
            organization,
            user,
            error_response,
        ) = OrganizationQueryMixin.get_organization_from_request(request)
        if error_response:
            return error_response

        try:
            booking = Booking.objects.get(
                id=booking_id, space__location__organization=organization
            )

            # Only allow cancellation if admin or booking owner
            if booking.user != user and user.role != Roles.ADMIN:
                return api_response(
                    message="You don't have permission to cancel this booking",
                    status_code=status.HTTP_403_FORBIDDEN,
                    success=False,
                    errors={
                        "permission": {
                            "code": ErrorCodes.PERMISSION_DENIED,
                            "message": "Access denied",
                        }
                    },
                )

            # Check if cancellation requires approval
            if booking.space.booking_policy.requires_approval_for_changes:
                booking.status = BookingStatus.PENDING_CANCELLATION
                message = "Cancellation request sent for approval"
            else:
                booking.status = BookingStatus.CANCELLED
                message = "Booking cancelled successfully"

            booking.save()

            return api_response(
                data={
                    "booking_id": str(booking.id),
                    "status": booking.status,
                    "cancelled_at": booking.updated_at,
                },
                message=message,
            )
        except Booking.DoesNotExist:
            return api_response(
                message="Booking not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )


class ExtendBookingView(APIView):
    permission_classes = [IsAuthenticated]

    def patch(self, request, booking_id):
        """Extend a booking's end time."""
        (
            organization,
            user,
            error_response,
        ) = OrganizationQueryMixin.get_organization_from_request(request)
        if error_response:
            return error_response

        new_end_time = request.data.get("new_end_time")
        reason = request.data.get("reason")

        if not new_end_time:
            return api_response(
                message="New end time is required",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "new_end_time": {
                        "code": ErrorCodes.REQUIRED_FIELD_MISSING,
                        "message": "New end time is required",
                    }
                },
            )

        try:
            booking = Booking.objects.get(
                id=booking_id, space__location__organization=organization
            )

            # Only allow extension if admin or booking owner
            if booking.user != user and user.role != Roles.ADMIN:
                return api_response(
                    message="You don't have permission to extend this booking",
                    status_code=status.HTTP_403_FORBIDDEN,
                    success=False,
                    errors={
                        "permission": {
                            "code": ErrorCodes.PERMISSION_DENIED,
                            "message": "Access denied",
                        }
                    },
                )

            # Validate the extension
            is_valid, error, error_code = BookingValidationService.validate_booking(
                booking.space, booking.start_time, new_end_time, booking.id
            )

            if not is_valid:
                return api_response(
                    message=error,
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={"validation": {"code": error_code, "message": error}},
                )

            # Check if extension requires approval
            if booking.space.booking_policy.requires_approval_for_changes:
                original_end_time = booking.end_time
                booking.extension_reason = reason
                booking.end_time = new_end_time
                booking.status = BookingStatus.PENDING_EXTENSION
                booking.save()

                return api_response(
                    data={
                        "booking_id": str(booking.id),
                        "original_end_time": original_end_time,
                        "new_end_time": booking.end_time,
                        "extension_reason": reason,
                        "updated_at": booking.updated_at,
                    },
                    message="Extension request sent for approval",
                )
            else:
                booking.extension_reason = reason
                booking.end_time = new_end_time
                booking.save()

                return api_response(
                    data={
                        "booking_id": str(booking.id),
                        "new_end_time": booking.end_time,
                        "extension_reason": reason,
                        "updated_at": booking.updated_at,
                    },
                    message="Booking extended successfully",
                )
        except Booking.DoesNotExist:
            return api_response(
                message="Booking not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )


class RescheduleBookingView(APIView):
    permission_classes = [IsAuthenticated]

    def patch(self, request, booking_id):
        """Reschedule a booking."""
        (
            organization,
            user,
            error_response,
        ) = OrganizationQueryMixin.get_organization_from_request(request)
        if error_response:
            return error_response

        new_start_time = request.data.get("new_start_time")
        new_end_time = request.data.get("new_end_time")
        reason = request.data.get("reason")

        if not new_start_time or not new_end_time:
            return api_response(
                message="New start and end times are required",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "time": {
                        "code": ErrorCodes.REQUIRED_FIELD_MISSING,
                        "message": "New start and end times are required",
                    }
                },
            )

        try:
            booking = Booking.objects.get(
                id=booking_id, space__location__organization=organization
            )

            # Only allow rescheduling if admin or booking owner
            if booking.user != user and user.role != Roles.ADMIN:
                return api_response(
                    message="You don't have permission to reschedule this booking",
                    status_code=status.HTTP_403_FORBIDDEN,
                    success=False,
                    errors={
                        "permission": {
                            "code": ErrorCodes.PERMISSION_DENIED,
                            "message": "Access denied",
                        }
                    },
                )

            # Validate the reschedule
            is_valid, error, error_code = BookingValidationService.validate_booking(
                booking.space, new_start_time, new_end_time, booking.id
            )

            if not is_valid:
                return api_response(
                    message=error,
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={"validation": {"code": error_code, "message": error}},
                )

            # Check if reschedule requires approval
            if booking.space.booking_policy.requires_approval_for_changes:
                original_start_time = booking.start_time
                original_end_time = booking.end_time

                booking.reschedule_reason = reason
                booking.start_time = new_start_time
                booking.end_time = new_end_time
                booking.status = BookingStatus.PENDING_RESCHEDULE
                booking.save()

                return api_response(
                    data={
                        "booking_id": str(booking.id),
                        "original_start_time": original_start_time,
                        "original_end_time": original_end_time,
                        "new_start_time": booking.start_time,
                        "new_end_time": booking.end_time,
                        "reschedule_reason": reason,
                        "updated_at": booking.updated_at,
                    },
                    message="Reschedule request sent for approval",
                )
            else:
                booking.reschedule_reason = reason
                booking.start_time = new_start_time
                booking.end_time = new_end_time
                booking.notifications_sent = True
                booking.save()

                return api_response(
                    data={
                        "booking_id": str(booking.id),
                        "new_start_time": booking.start_time,
                        "new_end_time": booking.end_time,
                        "reschedule_reason": reason,
                        "updated_at": booking.updated_at,
                    },
                    message="Booking rescheduled successfully",
                )
        except Booking.DoesNotExist:
            return api_response(
                message="Booking not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )


class ApproveBookingView(OrganizationPermissionMixin, APIView):
    permission_category = PermissionCategories.BOOKING
    permission_action = PermissionActions.MANAGE

    def patch(self, request, organization_id, booking_id):
        """Approve a booking."""
        try:
            booking = Booking.objects.get(
                id=booking_id, space__location__organization=request.organization
            )

            # Check if booking is in a state that can be approved
            if booking.status not in [
                BookingStatus.PENDING,
                BookingStatus.PENDING_CHANGES,
                BookingStatus.PENDING_CANCELLATION,
            ]:
                return api_response(
                    message=f"Booking with status {booking.status} cannot be approved",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={
                        "status": {
                            "code": ErrorCodes.INVALID_BOOKING_STATUS,
                            "message": "Invalid booking status for approval",
                        }
                    },
                )

            # Determine new status based on current status
            if booking.status == BookingStatus.PENDING_CANCELLATION:
                booking.status = BookingStatus.CANCELLED
                message = "Booking cancellation approved"
            else:
                booking.status = BookingStatus.CONFIRMED
                message = "Booking approved successfully"

            booking.save()

            return api_response(
                data={
                    "booking_id": str(booking.id),
                    "status": booking.status,
                    "approved_at": booking.updated_at,
                },
                message=message,
            )
        except Booking.DoesNotExist:
            return api_response(
                message="Booking not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )


class RejectBookingView(OrganizationPermissionMixin, APIView):
    permission_category = PermissionCategories.BOOKING
    permission_action = PermissionActions.MANAGE

    def patch(self, request, organization_id, booking_id):
        """Reject a booking."""
        try:
            booking = Booking.objects.get(
                id=booking_id, space__location__organization=request.organization
            )

            # Check if booking is in a state that can be rejected
            if booking.status not in [
                BookingStatus.PENDING,
                BookingStatus.PENDING_CHANGES,
                BookingStatus.PENDING_CANCELLATION,
            ]:
                return api_response(
                    message=f"Booking with status {booking.status} cannot be rejected",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={
                        "status": {
                            "code": ErrorCodes.INVALID_BOOKING_STATUS,
                            "message": "Invalid booking status for rejection",
                        }
                    },
                )

            # Get rejection reason
            rejection_reason = request.data.get("reason", "")

            # Determine new status based on current status
            if booking.status == BookingStatus.PENDING_CANCELLATION:
                booking.status = BookingStatus.CONFIRMED  # Cancellation rejected
                message = "Cancellation request rejected"
            else:
                booking.status = BookingStatus.REJECTED
                message = "Booking rejected"

            booking.rejection_reason = rejection_reason
            booking.save()

            return api_response(
                data={
                    "booking_id": str(booking.id),
                    "status": booking.status,
                    "rejected_at": booking.updated_at,
                    "rejection_reason": booking.rejection_reason,
                },
                message=message,
            )
        except Booking.DoesNotExist:
            return api_response(
                message="Booking not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
            )


class CheckSpaceAvailabilityView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Check if a space is available for a given time range."""
        (
            organization,
            user,
            error_response,
        ) = OrganizationQueryMixin.get_organization_from_request(request)
        if error_response:
            return error_response

        space_slug = request.GET.get("space")
        start_time = request.GET.get("start_time")
        end_time = request.GET.get("end_time")

        if not all([space_slug, start_time, end_time]):
            return api_response(
                message="Space slug, start time, and end time are required",
                status_code=status.HTTP_400_BAD_REQUEST,
                success=False,
                errors={
                    "parameters": {
                        "code": ErrorCodes.REQUIRED_FIELD_MISSING,
                        "message": "Space slug, start time, and end time are required",
                    }
                },
            )

        try:
            space = Space.objects.get(
                slug=space_slug, location__organization=organization
            )
        except Space.DoesNotExist:
            return api_response(
                message="Space not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "space": {
                        "code": ErrorCodes.SPACE_NOT_FOUND,
                        "message": "Space not found",
                    }
                },
            )

        is_valid, error, error_code = BookingValidationService.validate_booking(
            space, start_time, end_time
        )

        if is_valid:
            return api_response(
                data={"is_available": True}, message="Space is available for booking"
            )
        else:
            return api_response(
                data={"is_available": False, "reason": error},
                message="Space is not available for booking",
            )
