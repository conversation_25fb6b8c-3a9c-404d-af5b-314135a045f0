from datetime import timedelta

from django.utils import timezone

from jechspace_backend.utils import ErrorCodes

from .models import Booking, BookingStatus


class BookingValidationService:
    @staticmethod
    def validate_time_slot_alignment(start_time, end_time, booking_interval):
        """
        Validates that booking start and end times align with the allowed time slots
        based on booking_interval (which defines the slot interval).

        Returns:
            tuple: (is_valid, error_message, error_code)
        """
        # Check if start time falls on a valid slot
        minutes_since_midnight = start_time.hour * 60 + start_time.minute
        if minutes_since_midnight % booking_interval != 0:
            return (
                False,
                f"Start time must be at {booking_interval}-minute intervals",
                ErrorCodes.INVALID_FORMAT,
            )

        # Check if end time falls on a valid slot
        minutes_since_midnight = end_time.hour * 60 + end_time.minute
        if minutes_since_midnight % booking_interval != 0:
            return (
                False,
                f"End time must be at {booking_interval}-minute intervals",
                ErrorCodes.INVALID_FORMAT,
            )

        return True, "", None

    @staticmethod
    def validate_booking(space, start_time, end_time, booking_id=None, user=None):
        """
        Validates a booking against space policies and availability

        Returns:
            tuple: (is_valid, error_message, error_code)
        """
        # Get booking policy
        try:
            policy = space.booking_policy
        except:
            return (
                False,
                "Space has no booking policy defined",
                ErrorCodes.LOCATION_POLICY_NOT_FOUND,
            )

        # Check duration
        duration_minutes = (end_time - start_time).total_seconds() / 60
        if duration_minutes < policy.min_duration_minutes:
            return (
                False,
                f"Booking must be at least {policy.min_duration_minutes} minutes",
                ErrorCodes.MIN_DURATION_EXCEEDS_MAX,
            )
        if duration_minutes > policy.max_duration_minutes:
            return (
                False,
                f"Booking cannot exceed {policy.max_duration_minutes} minutes",
                ErrorCodes.MIN_DURATION_EXCEEDS_MAX,
            )

        # Check advance booking time
        advance_minutes = policy.advance_booking_minutes
        if start_time < (timezone.now() + timedelta(minutes=advance_minutes)):
            return (
                False,
                f"Bookings must be made {advance_minutes} minutes in advance",
                ErrorCodes.NEGATIVE_ADVANCE_BOOKING,
            )

        # Check day availability
        day_of_week = start_time.strftime("%A").lower()
        availability = space.availability.filter(day=day_of_week).first()
        if not availability:
            return (
                False,
                f"Space not available on {day_of_week}",
                ErrorCodes.SPACE_NOT_AVAILABLE,
            )

        # Check time availability
        if (
            start_time.time() < availability.open_time
            or end_time.time() > availability.close_time
        ):
            return (
                False,
                "Booking outside available hours",
                ErrorCodes.NEGATIVE_BOOKING_INTERVAL,
            )

        # Check time slot alignment
        (
            is_aligned,
            error_msg,
            error_code,
        ) = BookingValidationService.validate_time_slot_alignment(
            start_time, end_time, policy.booking_interval
        )
        if not is_aligned:
            return False, error_msg, error_code

        # Check conflicts (overlapping bookings) for the space
        active_statuses = [BookingStatus.PENDING, BookingStatus.CONFIRMED]
        conflicting = Booking.objects.filter(
            space=space,
            status__in=active_statuses,
            start_time__lte=end_time,
            end_time__gte=start_time,
        )
        if booking_id:
            conflicting = conflicting.exclude(id=booking_id)

        if space.type == "desk":
            if conflicting.count() > space.capacity:
                return (
                    False,
                    "This space is already booked during the requested time.",
                    ErrorCodes.DESK_BOOKING_SINGLE_USER_ONLY,
                )

        if conflicting.exists() and space.type != "desk":
            return (
                False,
                "This space is already booked during the requested time.",
                ErrorCodes.SPACE_ALREADY_BOOKED,
            )

        # Check if user has overlapping bookings for any space
        if user:
            user_conflicting = Booking.objects.filter(
                user=user,
                status__in=active_statuses,
                start_time__lte=end_time,
                end_time__gte=start_time,
            )

            if booking_id:
                user_conflicting = user_conflicting.exclude(id=booking_id)

            if user_conflicting.exists():
                return (
                    False,
                    "You already have another booking during this time period.",
                    ErrorCodes.USER_ALREADY_BOOKED,
                )

        return True, "", None
