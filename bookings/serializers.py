from rest_framework import serializers

from jechspace_backend.utils import ErrorCodes
from spaces.models import Space
from users.models import Roles

from .models import Booking, BookingStatus
from .services import BookingValidationService


class SpaceSerializer(serializers.ModelSerializer):
    """Serializer for Space details inside Booking."""

    class Meta:
        model = Space
        fields = ["id", "name", "location", "type", "capacity", "slug"]


class BookingSerializer(serializers.ModelSerializer):
    """
    Serializer for Booking model with custom validation.

    This serializer handles:
    1. Space validation - ensures space exists in the organization
    2. Attendee validation - enforces desk space limitations
    3. Booking time validation - validates time against space availability and policy
    """

    space = SpaceSerializer(read_only=True)
    space_slug = serializers.CharField(write_only=True, required=True)
    user = serializers.SerializerMethodField()

    class Meta:
        model = Booking
        fields = [
            "id",
            "space",
            "space_slug",
            "user",
            "title",
            "description",
            "start_time",
            "end_time",
            "status",
            "attendees_count",
            "attendees",
            "is_recurring",
            "recurring_pattern",
            "check_in_code",
            "created_at",
            "updated_at",
            "extension_reason",
            "reschedule_reason",
            "notifications_sent",
        ]
        read_only_fields = [
            "id",
            "check_in_code",
            "created_at",
            "updated_at",
            "notifications_sent",
        ]

    def get_user(self, obj):
        """
        Custom method to serialize user details.

        Returns a dictionary with user information instead of just the ID.
        """
        return {
            "id": str(obj.user.id),
            "name": obj.user.get_full_name() or obj.user.username,
            "email": obj.user.email,
            "role": getattr(obj.user, "role", Roles.LEARNER),
            "department": getattr(obj.user, "department", None),
        }

    def validate_space_slug(self, value):
        """
        Validates the space_slug field by checking if a Space with the given slug exists.

        Args:
            value (str): The space slug to validate

        Returns:
            Space: The validated Space object

        Raises:
            ValidationError: If no Space with the provided slug exists in the organization
        """
        try:
            space = Space.objects.get(
                slug=value, location__organization=self.context.get("organization")
            )
            self.context["space"] = space
            return value
        except Space.DoesNotExist:
            raise serializers.ValidationError(
                "Space with this slug does not exist.", code=ErrorCodes.SPACE_NOT_FOUND
            )

    def validate_attendees_count(self, value):
        """
        Validates attendees count against space capacity and type.

        Args:
            value (int): The number of attendees

        Returns:
            int: The validated attendees count

        Raises:
            ValidationError: If desk spaces are booked for multiple users
        """
        space = self.context.get("space")
        if not space:
            return value

        if space.type == "desk" and value > 1:
            raise serializers.ValidationError(
                "You can only book desk spaces for yourself.",
                code=ErrorCodes.DESK_BOOKING_SINGLE_USER_ONLY,
            )

        if value > space.capacity:
            raise serializers.ValidationError(
                f"Attendees count exceeds space capacity of {space.capacity}.",
                code=ErrorCodes.ATTENDEES_COUNT_MISMATCH,
            )

        return value

    def validate_attendees(self, value):
        """
        Validates that attendees list matches space policy.

        Args:
            value (list): List of attendee emails or IDs

        Returns:
            list: The validated attendees list

        Raises:
            ValidationError: If desk spaces are assigned multiple attendees
        """
        space = self.context.get("space")
        if not space:
            return value

        if space.type == "desk" and value:
            raise serializers.ValidationError(
                "You can only book desk spaces for yourself.",
                code=ErrorCodes.DESK_BOOKING_SINGLE_USER_ONLY,
            )
        return value

    def validate(self, data):
        """
        Performs cross-field validation for the Booking serializer.

        This method:
        1. Validates attendees count matches attendees list length
        2. Validates booking times against space availability
        3. Sets initial status based on space booking policy

        Args:
            data (dict): The dictionary of field values to validate

        Returns:
            dict: The validated data with any modifications

        Raises:
            ValidationError: If validation fails for any reason
        """
        # Get space object from the slug
        space_slug = data.pop("space_slug", None)
        space = self.context.get("space")
        if not space and not space_slug:
            raise serializers.ValidationError(
                "Space is required.", code=ErrorCodes.SPACE_NOT_FOUND
            )
        data["space"] = space

        # Validate attendees and attendees_count consistency
        attendees = data.get("attendees", [])
        attendees_count = data.get("attendees_count", 1)

        if len(attendees) > 0 and len(attendees) != attendees_count:
            raise serializers.ValidationError(
                "Attendees count must match the number of attendees provided.",
                code=ErrorCodes.ATTENDEES_COUNT_MISMATCH,
            )

        # Get current user from context
        user = self.context.get("user") if self.context.get("user") else None

        # Validate booking time against space availability and user's existing bookings
        booking_id = self.instance.id if self.instance else None
        is_valid, error, error_code = BookingValidationService.validate_booking(
            space, data["start_time"], data["end_time"], booking_id, user
        )

        if not is_valid:
            raise serializers.ValidationError(
                {"booking": {"message": error, "code": error_code}}
            )

        # Set initial status based on approval policy
        if not booking_id and space.booking_policy.requires_approval:
            data["status"] = BookingStatus.PENDING
        elif not booking_id:
            data["status"] = BookingStatus.CONFIRMED

        return data

    def create(self, validated_data):
        """
        Creates a Booking instance with the validated data.

        Args:
            validated_data (dict): The validated data for creating the booking

        Returns:
            Booking: The newly created booking instance
        """
        # TODO: Generate check-in code logic

        # Ensure user is set from context if not already provided
        if "user" not in validated_data:
            # Try to get user from both possible context locations
            if self.context.get("user"):
                validated_data["user"] = self.context.get("user")
            elif self.context.get("request"):
                validated_data["user"] = self.context.get("request").user

        # Create the booking
        booking = Booking.objects.create(**validated_data)

        return booking
