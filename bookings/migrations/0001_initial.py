# Generated by Django 5.2 on 2025-06-03 12:56

import uuid

from django.db import migrations, models


class Migration(migrations.Migration):
    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Booking",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("title", models.CharField(blank=True, max_length=255, null=True)),
                ("description", models.TextField(blank=True, null=True)),
                ("start_time", models.DateTimeField()),
                ("end_time", models.DateTimeField()),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("confirmed", "Confirmed"),
                            ("cancelled", "Cancelled"),
                            ("completed", "Completed"),
                            ("rejected", "Rejected"),
                            ("pending_cancellation", "Pending Cancellation"),
                            ("pending_extension", "Pending Extension"),
                            ("pending_reschedule", "Pending Reschedule"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("attendees_count", models.PositiveIntegerField(default=1)),
                ("attendees", models.JSONField(blank=True, null=True)),
                ("is_recurring", models.BooleanField(default=False)),
                ("recurring_pattern", models.JSONField(blank=True, null=True)),
                (
                    "check_in_code",
                    models.CharField(blank=True, max_length=10, null=True),
                ),
                ("extension_reason", models.TextField(blank=True, null=True)),
                ("reschedule_reason", models.TextField(blank=True, null=True)),
                ("notifications_sent", models.BooleanField(default=False)),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
