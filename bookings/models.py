from django.db import models

from core.models import BaseModel


# Booking status constants
class BookingStatus:
    PENDING = "pending"
    CONFIRMED = "confirmed"
    CANCELLED = "cancelled"
    COMPLETED = "completed"
    REJECTED = "rejected"
    PENDING_CANCELLATION = "pending_cancellation"
    PENDING_EXTENSION = "pending_extension"
    PENDING_RESCHEDULE = "pending_reschedule"


class Booking(BaseModel):
    """Model for managing bookings for spaces."""

    STATUS_CHOICES = (
        (BookingStatus.PENDING, "Pending"),
        (BookingStatus.CONFIRMED, "Confirmed"),
        (BookingStatus.CANCELLED, "Cancelled"),
        (BookingStatus.COMPLETED, "Completed"),
        (BookingStatus.REJECTED, "Rejected"),
        (BookingStatus.PENDING_CANCELLATION, "Pending Cancellation"),
        (BookingStatus.PENDING_EXTENSION, "Pending Extension"),
        (BookingStatus.PENDING_RESCHEDULE, "Pending Reschedule"),
    )

    space = models.ForeignKey(
        "spaces.Space", on_delete=models.CASCADE, related_name="bookings"
    )
    user = models.ForeignKey(
        "users.User", on_delete=models.CASCADE, related_name="bookings"
    )
    title = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default=BookingStatus.PENDING
    )
    attendees_count = models.PositiveIntegerField(default=1)
    attendees = models.JSONField(
        blank=True, null=True
    )  # List of attendee emails and their status
    is_recurring = models.BooleanField(default=False)
    recurring_pattern = models.JSONField(
        blank=True, null=True
    )  # frequency, days, until_date
    check_in_code = models.CharField(max_length=10, blank=True, null=True)
    extension_reason = models.TextField(blank=True, null=True)
    reschedule_reason = models.TextField(blank=True, null=True)
    notifications_sent = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.title or 'Booking'} - {self.space.name}"
