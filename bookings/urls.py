from django.urls import path

from .views import (
    BookingDetailView,
    BookingCreateListView,
    CancelBookingView,
    ExtendBookingView,
    RescheduleBookingView,
    ApproveBookingView,
    RejectBookingView,
    CheckSpaceAvailabilityView
)

urlpatterns = [
    path("", BookingCreateListView.as_view(), name="booking-list-create"),
    path("<uuid:booking_id>/", BookingDetailView.as_view(), name="booking-detail"),
    path("<uuid:booking_id>/cancel/", CancelBookingView.as_view(), name="booking-cancel"),
    path("<uuid:booking_id>/extend/", ExtendBookingView.as_view(), name="booking-extend"),
    path("<uuid:booking_id>/reschedule/", RescheduleBookingView.as_view(), name="booking-reschedule"),
    path("<uuid:booking_id>/approve/", ApproveBookingView.as_view(), name="booking-approve"),
    path("<uuid:booking_id>/reject/", RejectBookingView.as_view(), name="booking-reject"),
    path("check-availability/", CheckSpaceAvailabilityView.as_view(), name="space-availability"),
]
