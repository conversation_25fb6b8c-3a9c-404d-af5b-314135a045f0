services:
  jechspace-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: jechspace-backend
    env_file:
      - .env
    ports:
      - "7632:7632"
    depends_on:
      - redis
      - mysql
    environment:
      - DB_HOST=mysql
      - DB_PORT=3306
      - REDIS_URL=redis://redis:6379/0
    networks:
      - jechspace-network

  redis:
    image: 'bitnami/redis:latest'
    container_name: jechspace-redis
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    ports:
      - "7379:6379"
    volumes:
      - redis_data:/bitnami/redis/data
    networks:
      - jechspace-network

  mysql:
    image: 'mysql:8.0'
    container_name: jechspace-mysql
    environment:
      - MYSQL_DATABASE=jechspace
      - MYSQL_USER=${MYSQL_USER}
      - MYSQL_PASSWORD=${MYSQL_PASSWORD}
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - jechspace-network

networks:
  jechspace-network:
    driver: bridge

volumes:
  redis_data:
    driver: local
  mysql_data:
    driver: local
