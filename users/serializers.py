from rest_framework import serializers

from .models import User, UserOrganization


class UserOrganizationSerializer(serializers.ModelSerializer):
    organization = serializers.SerializerMethodField()

    class Meta:
        model = UserOrganization
        fields = [
            "id",
            "organization",
            "role",
            "department",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def get_organization(self, obj):
        # Import here to avoid circular import
        from organizations.serializers import OrganizationListSerializer

        return OrganizationListSerializer(obj.organization).data


class UserSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()
    organizations = UserOrganizationSerializer(many=True, read_only=True)

    class Meta:
        model = User
        fields = [
            "id",
            "first_name",
            "last_name",
            "full_name",
            "email",
            "is_verified",
            "profile_picture",
            "phone_number",
            "profession",
            "bio",
            "organizations",
            "is_active",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "is_verified",
            "is_active",
            "created_at",
            "updated_at",
        ]

    def get_full_name(self, obj):
        return obj.get_full_name()
