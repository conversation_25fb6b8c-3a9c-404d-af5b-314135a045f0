from django.contrib.auth import get_user_model
from django.contrib.auth.backends import ModelBackend

from organizations.models import Organization

User = get_user_model()


class OrganizationBackend(ModelBackend):
    """
    Custom authentication backend that handles email, password, and organization.
    It authenticates a user based on the combination of email, password and organization.
    """

    def authenticate(
        self, request, email=None, password=None, organization=None, **kwargs
    ):
        if email is None or password is None:
            return None

        try:
            # Get users by email - there could be multiple users with same email in different organizations
            users = User.objects.filter(email=email)

            if not users.exists():
                return None

            # Handle organization validation
            if organization == "null":
                # For admin login without organization context (for super admins)
                for user in users:
                    if (
                        user.check_password(password)
                        and user.role == "admin"
                        and not user.organization
                    ):
                        return user
                return None
            elif organization:
                # Check if organization is a string (slug) or an Organization object
                if isinstance(organization, str):
                    try:
                        org_obj = Organization.objects.get(slug=organization)
                    except Organization.DoesNotExist:
                        return None
                else:
                    org_obj = organization

                # Find user that belongs to this organization
                for user in users:
                    if user.check_password(password) and user.organization == org_obj:
                        return user
                return None
            else:
                return None

        except Exception as e:
            return None

    def get_user(self, user_id):
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
