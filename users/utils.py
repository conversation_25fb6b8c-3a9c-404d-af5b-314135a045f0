"""
Utility functions and Objects for users app
"""
from django.utils.crypto import get_random_string

# List of disallowed public email domains
DISALLOWED_EMAIL_DOMAINS = [
    "gmail.com",
    "yahoo.com",
    "outlook.com",
    "hotmail.com",
    "aol.com",
    "mail.com",
]


def generate_user_token(user, purpose):
    """
    Generate a user token for a user and save it to the database

    Args:
        user: The user to generate a token for

    Returns:
        str: The generated token
    """
    # Import inside function to avoid circular import
    from .models import UserToken

    token = get_random_string(length=32)
    # TODO: try again if an error occurs
    UserToken.objects.create(token=token, user=user, purpose=purpose)

    return token
