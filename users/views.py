from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import generics, permissions, status
from rest_framework.parsers import <PERSON><PERSON>ars<PERSON>, MultiPartParser

from jechspace_backend.utils import api_response

from .serializers import UserSerializer


class UserMeView(generics.RetrieveAPIView):
    """
    Retrieve or update the authenticated user's profile.
    """

    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def get_object(self):
        return self.request.user

    @swagger_auto_schema(
        operation_summary="Get authenticated user profile",
        operation_description="Returns the profile of the currently authenticated user, including personal information and organizations.",
        responses={
            200: openapi.Response(
                description="User profile retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="User profile retrieved successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "first_name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="John"
                                ),
                                "last_name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="Doe"
                                ),
                                "full_name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="John Doe"
                                ),
                                "email": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="<EMAIL>"
                                ),
                                "is_verified": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=True
                                ),
                                "profile_picture": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="https://example.com/media/profile_pictures/image.jpg",
                                    nullable=True,
                                ),
                                "phone_number": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="+1234567890",
                                    nullable=True,
                                ),
                                "profession": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="Software Engineer",
                                ),
                                "bio": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="Passionate about technology",
                                    nullable=True,
                                ),
                                "organizations": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_OBJECT,
                                        properties={
                                            "id": openapi.Schema(
                                                type=openapi.TYPE_STRING,
                                                example="550e8400-e29b-41d4-a716-446655440000",
                                            ),
                                            "organization": openapi.Schema(
                                                type=openapi.TYPE_OBJECT,
                                                properties={
                                                    "id": openapi.Schema(
                                                        type=openapi.TYPE_STRING,
                                                        example="550e8400-e29b-41d4-a716-446655440000",
                                                    ),
                                                    "name": openapi.Schema(
                                                        type=openapi.TYPE_STRING,
                                                        example="Acme Corp",
                                                    ),
                                                },
                                            ),
                                            "role": openapi.Schema(
                                                type=openapi.TYPE_STRING,
                                                example="admin",
                                                description="User role in the organization. Possible values: admin, member",
                                            ),
                                            "department": openapi.Schema(
                                                type=openapi.TYPE_STRING,
                                                example="Engineering",
                                                nullable=True,
                                            ),
                                        },
                                    ),
                                ),
                                "is_active": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=True
                                ),
                                "created_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                                "updated_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                            },
                        ),
                    },
                ),
            ),
            401: openapi.Response(
                description="Authentication credentials were not provided or are invalid"
            ),
        },
        tags=["User Profile"],
    )
    def get(self, request, *args, **kwargs):
        serializer = self.get_serializer(self.get_object())
        return api_response(
            data=serializer.data,
            message="User profile retrieved successfully",
            status_code=status.HTTP_200_OK,
        )

    @swagger_auto_schema(
        operation_summary="Update authenticated user profile",
        operation_description="Updates the profile of the currently authenticated user. Supports partial updates with multipart/form-data for file uploads.",
        manual_parameters=[
            openapi.Parameter(
                "first_name",
                openapi.IN_FORM,
                description="User's first name",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "last_name",
                openapi.IN_FORM,
                description="User's last name",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "phone_number",
                openapi.IN_FORM,
                description="User's phone number",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "profession",
                openapi.IN_FORM,
                description="User's profession",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "bio",
                openapi.IN_FORM,
                description="User's bio",
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                "profile_picture",
                openapi.IN_FORM,
                description="User's profile picture",
                type=openapi.TYPE_FILE,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                description="Profile updated successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="success"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING,
                            example="Profile updated successfully",
                        ),
                        "data": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "id": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="550e8400-e29b-41d4-a716-446655440000",
                                ),
                                "first_name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="John"
                                ),
                                "last_name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="Doe"
                                ),
                                "full_name": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="John Doe"
                                ),
                                "email": openapi.Schema(
                                    type=openapi.TYPE_STRING, example="<EMAIL>"
                                ),
                                "is_verified": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=True
                                ),
                                "profile_picture": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="https://example.com/media/profile_pictures/image.jpg",
                                    nullable=True,
                                ),
                                "phone_number": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="+1234567890",
                                    nullable=True,
                                ),
                                "profession": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="Software Engineer",
                                ),
                                "bio": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    example="Passionate about technology",
                                    nullable=True,
                                ),
                                "organizations": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(type=openapi.TYPE_OBJECT),
                                ),
                                "is_active": openapi.Schema(
                                    type=openapi.TYPE_BOOLEAN, example=True
                                ),
                                "created_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                                "updated_at": openapi.Schema(
                                    type=openapi.TYPE_STRING,
                                    format="date-time",
                                    example="2023-01-01T12:00:00Z",
                                ),
                            },
                        ),
                    },
                ),
            ),
            400: openapi.Response(
                description="Invalid data provided",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "status": openapi.Schema(
                            type=openapi.TYPE_STRING, example="error"
                        ),
                        "message": openapi.Schema(
                            type=openapi.TYPE_STRING, example="Invalid data provided"
                        ),
                        "errors": openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                "first_name": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="This field is required.",
                                    ),
                                ),
                                "profile_picture": openapi.Schema(
                                    type=openapi.TYPE_ARRAY,
                                    items=openapi.Schema(
                                        type=openapi.TYPE_STRING,
                                        example="Invalid image format.",
                                    ),
                                ),
                            },
                        ),
                    },
                ),
            ),
            401: openapi.Response(
                description="Authentication credentials were not provided or are invalid"
            ),
        },
        tags=["User Profile"],
    )
    def patch(self, request, *args, **kwargs):
        user = self.get_object()
        serializer = self.get_serializer(user, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return api_response(
            data=serializer.data,
            message="Profile updated successfully",
            status_code=status.HTTP_200_OK,
        )
