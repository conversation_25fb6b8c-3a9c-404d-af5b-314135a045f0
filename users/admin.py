from django.contrib import admin
from django.contrib.auth.admin import UserAdmin

from .models import UserToken, User


# CustomUser Admin
class CustomUserAdmin(UserAdmin):
    list_display = ("email", "first_name", "last_name", "is_verified", "profile_picture")
    search_fields = (
        "email",
        "first_name",
        "last_name",
    )  # Add search functionality
    list_filter = ("is_verified",)
    ordering = ("email",)

    # These fields are required for UserAdmin when using a custom User model
    fieldsets = (
        (None, {"fields": ("email", "password")}),
        (
            "Personal info",
            {"fields": ("first_name", "last_name", "phone_number", "profile_picture", "profession", "bio")},
        ),
        (
            "Permissions",
            {
                "fields": (
                    "is_verified",
                    "is_superuser",
                )
            },
        ),
        ("Important dates", {"fields": ("last_login", "created_at")}),
    )

    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "email",
                    "password1",
                    "password2",
                    "first_name",
                    "last_name",
                    "phone_number",
                    "profession",
                ),
            },
        ),
    )


admin.site.register(User, CustomUserAdmin)


# UserToken Admin
class EmailVerificationTokenAdmin(admin.ModelAdmin):
    list_display = ("token", "user", "created_at", "expires_at", "used")
    search_fields = ("token", "user__email")
    list_filter = ("used", "created_at")


admin.site.register(UserToken, EmailVerificationTokenAdmin)
