"""
Custom validators for the users app
"""
import re

from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _

# Import our custom error codes
from jechspace_backend.utils import ErrorCodes


class ComplexPasswordValidator:
    """
    Validate that the password meets complexity requirements:
    - At least one uppercase letter
    - At least one lowercase letter
    - At least one digit
    - At least one special character
    - Minimum length of 8 characters
    """

    def __init__(self, min_length=8):
        self.min_length = min_length

    def validate(self, password, user=None):
        if len(password) < self.min_length:
            raise ValidationError(
                _(f"Password must be at least {self.min_length} characters long."),
                code=ErrorCodes.PASSWORD_TOO_SHORT,
            )

        if not re.search(r"[A-Z]", password):
            raise ValidationError(
                _("Password must contain at least one uppercase letter."),
                code=ErrorCodes.PASSWORD_NO_UPPERCASE,
            )

        if not re.search(r"[a-z]", password):
            raise ValidationError(
                _("Password must contain at least one lowercase letter."),
                code=ErrorCodes.PASSWORD_NO_LOWERCASE,
            )

        if not re.search(r"[0-9]", password):
            raise ValidationError(
                _("Password must contain at least one digit."), 
                code=ErrorCodes.PASSWORD_NO_DIGIT,
            )

        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise ValidationError(
                _(
                    'Password must contain at least one special character (!@#$%^&*(),.?":{}|<>).'
                ),
                code=ErrorCodes.PASSWORD_NO_SPECIAL,
            )

    def get_help_text(self):
        return _(
            "Your password must be at least 8 characters long, "
            "contain at least one uppercase letter, one lowercase letter, "
            "one digit, and one special character."
        )
