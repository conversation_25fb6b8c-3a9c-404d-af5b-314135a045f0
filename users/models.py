from datetime import timedelta

from django.conf import settings
from django.contrib.auth.models import (
    AbstractBaseUser,
    BaseUserManager,
    PermissionsMixin,
)
from django.db import models
from django.utils import timezone

from core.models import BaseModel


# Role constants
class Roles:
    OWNER = "owner"
    ADMIN = "admin"
    EMPLOYEE = "employee"
    MEMBER = "member"


ROLE_CHOICES = (
    (Roles.ADMIN, "Admin"),
    (Roles.OWNER, "Owner"),
    (Roles.EMPLOYEE, "Employee"),
    (Roles.MEMBER, "Member"),
)


# User type constants
class UserType:
    INDIVIDUAL = "individual"
    ORGANIZATION_OWNER = "organization_owner"

    CHOICES = (
        (INDIVIDUAL, "Individual"),
        (ORGANIZATION_OWNER, "Organization Owner"),
    )


def get_expiry_date():
    return timezone.now() + timedelta(minutes=30)


class UserManager(BaseUserManager):
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError("Users must have an email address")
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields["is_staff"] = True
        extra_fields["is_superuser"] = True
        extra_fields["is_active"] = True

        return self.create_user(email, password, **extra_fields)


class User(BaseModel, AbstractBaseUser, PermissionsMixin):
    email = models.EmailField(unique=True)
    first_name = models.CharField(max_length=150, blank=False, null=False)
    last_name = models.CharField(max_length=150, blank=False, null=False)
    is_verified = models.BooleanField(default=False)
    profile_picture = models.ImageField(
        upload_to="profile_pictures/", null=True, blank=True
    )
    phone_number = models.CharField(max_length=15, blank=True, null=True)
    profession = models.CharField(max_length=100, blank=False, null=False)
    bio = models.TextField(max_length=170, blank=True)
    user_type = models.CharField(
        max_length=20, choices=UserType.CHOICES, default=UserType.INDIVIDUAL
    )

    objects = UserManager()

    USERNAME_FIELD = "email"

    def __str__(self):
        return f"{self.email} [{self.first_name} - {self.last_name}]"

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

    def to_dict(self):
        return {
            "id": self.id,
            "email": self.email,
            "first_name": self.first_name,
            "last_name": self.last_name,
            "profession": self.profession,
            "bio": self.bio,
            "profile_picture": self.profile_picture.url
            if self.profile_picture
            else None,
            "phone_number": self.phone_number,
            "is_verified": self.is_verified,
            "user_type": self.user_type,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "organizations": [
                organization.to_dict() for organization in self.organizations.all()
            ],
        }


class UserOrganization(BaseModel):
    user = models.ForeignKey(
        User, on_delete=models.CASCADE, related_name="organizations"
    )
    organization = models.ForeignKey(
        "organizations.Organization", on_delete=models.CASCADE, related_name="users"
    )
    role = models.CharField(max_length=10, choices=ROLE_CHOICES)
    department = models.ForeignKey(
        "departments.Department",
        on_delete=models.SET_NULL,
        related_name="members",
        null=True,
        blank=True,
    )

    class Meta:
        unique_together = ("user", "organization")

    def to_dict(self):
        return {
            "id": self.organization.id,
            "slug": self.organization.slug,
            "role": self.role,
            "department": self.department.name if self.department else None,
            "department_id": str(self.department.id) if self.department else None,
            "industry": self.organization.industry,
        }


class UserToken(BaseModel):
    class Purpose(models.TextChoices):
        EMAIL_VERIFICATION = "email_verification", "Email Verification"
        PASSWORD_RESET = "password_reset", "Password Reset"

    token = models.CharField(max_length=255, unique=True)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="user_tokens",
    )
    purpose = models.CharField(max_length=32, choices=Purpose.choices)
    expires_at = models.DateTimeField(default=get_expiry_date)
    used = models.BooleanField(default=False)

    def __str__(self):
        return f"Token for {self.user.email} ({self.purpose}) (Used: {self.used})"
