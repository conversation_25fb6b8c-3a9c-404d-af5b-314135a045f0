# JechSpace API Documentation

## Overview

JechSpace is a comprehensive workspace management platform that enables organizations to efficiently manage their physical spaces, bookings, and resources. This API provides endpoints for authentication, organization management, space booking, and user management.

## Base URL

```
Production: https://api-staging.jechspace.com/api/v1/
Development: http://localhost:8000/api/v1/
```

## Authentication

JechSpace API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```http
Authorization: Bearer <your-access-token>
```

### Token Lifecycle

- **Access Token**: Valid for 30 minutes
- **Refresh Token**: Valid for 7 days
- Use the refresh token to obtain new access tokens

## User Types

The API supports two main user types:

### Individual Users

- Personal users who can book spaces
- Can join organizations as members
- Limited to personal workspace bookings

### Organization Owners

- Can create and manage organizations
- Full administrative capabilities
- Must use company email addresses (not common providers like Gmail)

## API Response Format

All API responses follow a consistent format:

```json
{
  "status": "success|error",
  "message": "Human readable message",
  "data": {},
  "errors": {}
}
```

### Success Response

```json
{
  "status": "success",
  "message": "Operation completed successfully",
  "data": {
    // Response data here
  }
}
```

### Error Response

```json
{
  "status": "error",
  "message": "Operation failed",
  "errors": {
    "field_name": ["Error message for this field"],
    "another_field": ["Another error message"]
  }
}
```

## Pagination

List endpoints support pagination with the following parameters:

- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)

Paginated responses include:

```json
{
  "status": "success",
  "message": "Items retrieved successfully",
  "data": {
    "count": 100,
    "next": "http://api-staging.jechspace.com/endpoint/?page=3",
    "previous": "http://api-staging.jechspace.com/endpoint/?page=1",
    "results": [
      // Array of items
    ]
  }
}
```

## Error Codes

The API uses specific error codes for different scenarios:

| Code                       | Description                                           |
| -------------------------- | ----------------------------------------------------- |
| `EMAIL_ALREADY_EXISTS`     | Email address is already registered                   |
| `INVALID_CREDENTIALS`      | Invalid email or password                             |
| `ACCOUNT_NOT_VERIFIED`     | Email verification required                           |
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions                       |
| `ORGANIZATION_NOT_FOUND`   | Organization does not exist                           |
| `USER_NOT_IN_ORGANIZATION` | User is not a member of the organization              |
| `COMMON_EMAIL_DOMAIN`      | Organization owners cannot use common email providers |

## Rate Limiting

API requests are rate-limited to ensure fair usage:

- **Authenticated requests**: 1000 requests per hour
- **Unauthenticated requests**: 100 requests per hour

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Endpoints Overview

### Authentication

- `POST /auth/signup/` - User registration
- `POST /auth/signin/` - User login
- `POST /auth/signout/` - User logout
- `POST /auth/email/verification/` - Request email verification
- `PATCH /auth/email/verification/` - Verify email with token
- `POST /auth/password/reset/` - Request password reset
- `PATCH /auth/password/reset/` - Reset password with token
- `PATCH /auth/password/` - Change password (authenticated)
- `POST /auth/token/refresh/` - Refresh access token

### Organizations

- `POST /organizations/create/` - Create organization
- `GET /organizations/` - List user's organizations
- `GET /organizations/public/` - List public organizations
- `GET /organizations/{id}/` - Get organization details
- `PATCH /organizations/{id}/update/` - Update organization
- `DELETE /organizations/{id}/delete/` - Delete organization
- `POST /organizations/{id}/leave/` - Leave organization
- `GET /organizations/{id}/members/` - List organization members
- `PATCH /organizations/{id}/members/{user_id}/` - Update member role
- `DELETE /organizations/{id}/members/{user_id}/remove/` - Remove member

### Users

- `GET /users/me/` - Get current user profile
- `PATCH /users/me/` - Update current user profile

### Statistics

- `GET /stats/global/` - Get global platform statistics (Admin only)
- `GET /stats/user/` - Get current user statistics
- `GET /organizations/{id}/stats/` - Get organization statistics

### Spaces

- `GET /spaces/` - List spaces
- `POST /spaces/` - Create space
- `GET /spaces/{id}/` - Get space details
- `PATCH /spaces/{id}/` - Update space
- `DELETE /spaces/{id}/` - Delete space

### Bookings

- `GET /bookings/` - List bookings
- `POST /bookings/` - Create booking
- `GET /bookings/{id}/` - Get booking details
- `POST /bookings/{id}/cancel/` - Cancel booking
- `POST /bookings/{id}/approve/` - Approve booking
- `POST /bookings/{id}/reject/` - Reject booking

### Locations

- `GET /organizations/{id}/locations/` - List organization locations
- `POST /organizations/{id}/locations/` - Create location
- `GET /organizations/{id}/locations/{location_id}/` - Get location details
- `PATCH /organizations/{id}/locations/{location_id}/` - Update location
- `DELETE /organizations/{id}/locations/{location_id}/` - Delete location

## Common Use Cases

### New User Signup (Individual)

```http
POST /auth/signup/?type=individual
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "confirm_password": "SecurePass123!",
    "first_name": "John",
    "last_name": "Doe",
    "phone_number": "+1234567890",
    "profession": "Software Engineer"
}
```

### New Organization Owner Signup

```http
POST /auth/signup/?type=organization
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "confirm_password": "SecurePass123!",
    "first_name": "Jane",
    "last_name": "Smith",
    "phone_number": "+**********",
    "profession": "Chief Executive Officer"
}
```

### Create Organization

```http
POST /organizations/create/
Authorization: Bearer <access-token>
Content-Type: application/json

{
    "name": "TechCorp Solutions",
    "description": "Leading technology solutions provider",
    "type": "private",
    "email": "<EMAIL>",
    "phone": "+**********",
    "website": "https://techcorp.com",
    "employee_size": "51-200",
    "industry": "Technology"
}
```

### Get Organization Statistics

```http
GET /organizations/{organization_id}/stats/
Authorization: Bearer <access-token>
```

## Interactive Documentation

For interactive API documentation with the ability to test endpoints directly:

- **Swagger UI**: `/swagger/`
- **ReDoc**: `/redoc/`

## Support

For API support and questions:

- **Email**: <EMAIL>
- **Documentation**: https://docs.jechspace.com
- **Status Page**: https://status.jechspace.com
