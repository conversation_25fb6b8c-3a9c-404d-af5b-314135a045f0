"""
Example data for JechSpace API documentation.
Contains realistic examples for different user scenarios and use cases.
"""

# Authentication Examples

# New Individual User Signup
INDIVIDUAL_SIGNUP_REQUEST = {
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "confirm_password": "SecurePass123!",
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "phone_number": "+**********",
    "profession": "Software Engineer"
}

INDIVIDUAL_SIGNUP_SUCCESS_RESPONSE = {
    "status": "success",
    "message": "User registered successfully",
    "data": {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "email": "<EMAIL>",
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON>",
        "phone_number": "+**********",
        "profession": "Software Engineer",
        "is_verified": False,
        "user_type": "individual",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z"
    }
}

# New Organization Owner Signup
ORGANIZATION_SIGNUP_REQUEST = {
    "email": "<EMAIL>",
    "password": "SecurePass123!",
    "confirm_password": "SecurePass123!",
    "first_name": "Jane",
    "last_name": "Smith",
    "phone_number": "+1987654321",
    "profession": "Chief Executive Officer"
}

ORGANIZATION_SIGNUP_SUCCESS_RESPONSE = {
    "status": "success",
    "message": "Organization owner registered successfully",
    "data": {
        "id": "660f9511-f3ac-52e5-b827-557766551111",
        "email": "<EMAIL>",
        "first_name": "Jane",
        "last_name": "Smith",
        "phone_number": "+1987654321",
        "profession": "Chief Executive Officer",
        "is_verified": False,
        "user_type": "organization_owner",
        "created_at": "2024-01-15T11:00:00Z",
        "updated_at": "2024-01-15T11:00:00Z"
    }
}

# Duplicate Signup Error
DUPLICATE_SIGNUP_ERROR_RESPONSE = {
    "status": "error",
    "message": "Validation Error",
    "errors": {
        "email": ["A user with this email already exists"]
    }
}

# Common Email Domain Error for Organization
COMMON_EMAIL_DOMAIN_ERROR_RESPONSE = {
    "status": "error",
    "message": "Validation Error",
    "errors": {
        "email": ["Organization owners must use a company email address, not a common email provider"]
    }
}

# Login Examples
LOGIN_REQUEST = {
    "email": "<EMAIL>",
    "password": "SecurePass123!"
}

LOGIN_SUCCESS_RESPONSE = {
    "status": "success",
    "message": "Login successful",
    "data": {
        "user": {
            "id": "550e8400-e29b-41d4-a716-446655440000",
            "email": "<EMAIL>",
            "first_name": "John",
            "last_name": "Doe"
        },
        "auth": {
            "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.example_signature",
            "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************.example_signature",
            "token_type": "Bearer",
            "expires_in": 1800
        }
    }
}

# Organization Examples
ORGANIZATION_CREATE_REQUEST = {
    "name": "TechCorp Solutions",
    "description": "Leading technology solutions provider",
    "type": "private",
    "email": "<EMAIL>",
    "phone": "+**********",
    "website": "https://techcorp.com",
    "employee_size": "51-200",
    "industry": "Technology"
}

ORGANIZATION_SUCCESS_RESPONSE = {
    "status": "success",
    "message": "Organization created successfully",
    "data": {
        "id": "770fa622-g4bd-63f6-c938-668877662222",
        "name": "TechCorp Solutions",
        "slug": "techcorp-solutions",
        "description": "Leading technology solutions provider",
        "type": "private",
        "logo": None,
        "email": "<EMAIL>",
        "phone": "+**********",
        "website": "https://techcorp.com",
        "employee_size": "51-200",
        "industry": "Technology",
        "created_at": "2024-01-15T12:00:00Z",
        "updated_at": "2024-01-15T12:00:00Z"
    }
}

# Statistics Examples
ALL_STATS_RESPONSE = {
    "status": "success",
    "message": "Statistics retrieved successfully",
    "data": {
        "overview": {
            "total_users": 1250,
            "total_organizations": 45,
            "total_spaces": 320,
            "total_bookings": 8750,
            "active_bookings": 125
        },
        "user_stats": {
            "individual_users": 980,
            "organization_owners": 45,
            "verified_users": 1180,
            "unverified_users": 70
        },
        "organization_stats": {
            "private_organizations": 38,
            "public_organizations": 7,
            "organizations_by_size": {
                "1-10": 15,
                "11-50": 18,
                "51-200": 8,
                "201-500": 3,
                "501-1000": 1,
                "1001-5000": 0,
                "5000+": 0
            }
        },
        "space_stats": {
            "available_spaces": 280,
            "occupied_spaces": 35,
            "maintenance_spaces": 5,
            "spaces_by_type": {
                "meeting_room": 120,
                "desk": 150,
                "office": 30,
                "conference_room": 20
            }
        },
        "booking_stats": {
            "pending_bookings": 25,
            "approved_bookings": 100,
            "rejected_bookings": 15,
            "cancelled_bookings": 180,
            "completed_bookings": 8430,
            "bookings_this_month": 450,
            "bookings_last_month": 520
        },
        "period": "2024-01-15T00:00:00Z to 2024-01-15T23:59:59Z"
    }
}

ORGANIZATION_ONLY_STATS_RESPONSE = {
    "status": "success",
    "message": "Organization statistics retrieved successfully",
    "data": {
        "organization_id": "770fa622-g4bd-63f6-c938-668877662222",
        "organization_name": "TechCorp Solutions",
        "overview": {
            "total_members": 85,
            "total_locations": 3,
            "total_spaces": 45,
            "total_bookings": 1250,
            "active_bookings": 18
        },
        "member_stats": {
            "active_members": 82,
            "inactive_members": 3,
            "members_by_role": {
                "owner": 1,
                "admin": 4,
                "employee": 65,
                "member": 15
            },
            "members_by_department": {
                "Engineering": 35,
                "Sales": 20,
                "Marketing": 15,
                "HR": 8,
                "Finance": 7
            }
        },
        "space_utilization": {
            "average_utilization": 78.5,
            "peak_hours": ["09:00-11:00", "14:00-16:00"],
            "most_booked_spaces": [
                {"space_name": "Conference Room A", "bookings": 45},
                {"space_name": "Meeting Room 1", "bookings": 38},
                {"space_name": "Desk 101", "bookings": 32}
            ]
        },
        "booking_trends": {
            "bookings_this_week": 65,
            "bookings_last_week": 58,
            "average_booking_duration": 90,
            "popular_booking_times": {
                "morning": 35,
                "afternoon": 45,
                "evening": 20
            }
        },
        "period": "2024-01-15T00:00:00Z to 2024-01-15T23:59:59Z"
    }
}

# Space Examples
SPACE_CREATE_REQUEST = {
    "name": "Conference Room Alpha",
    "description": "Large conference room with video conferencing capabilities",
    "space_type": "conference_room",
    "capacity": 12,
    "floor": "2nd Floor",
    "room": "Room 201",
    "amenities": ["projector", "whiteboard", "video_conference", "wifi"]
}

SPACE_SUCCESS_RESPONSE = {
    "status": "success",
    "message": "Space created successfully",
    "data": {
        "id": "880fb733-h5ce-74g7-d049-779988773333",
        "name": "Conference Room Alpha",
        "slug": "conference-room-alpha",
        "description": "Large conference room with video conferencing capabilities",
        "space_type": "conference_room",
        "capacity": 12,
        "status": "available",
        "is_active": True,
        "address": {
            "floor": "2nd Floor",
            "room": "Room 201",
            "desk": None
        },
        "amenities": ["projector", "whiteboard", "video_conference", "wifi"],
        "created_at": "2024-01-15T13:00:00Z",
        "updated_at": "2024-01-15T13:00:00Z"
    }
}

# Booking Examples
BOOKING_CREATE_REQUEST = {
    "space_id": "880fb733-h5ce-74g7-d049-779988773333",
    "start_time": "2024-01-20T09:00:00Z",
    "end_time": "2024-01-20T11:00:00Z",
    "purpose": "Team standup meeting",
    "notes": "Weekly team sync with remote participants"
}

BOOKING_SUCCESS_RESPONSE = {
    "status": "success",
    "message": "Booking created successfully",
    "data": {
        "id": "990fc844-i6df-85h8-e15a-88aa99884444",
        "space": "880fb733-h5ce-74g7-d049-779988773333",
        "user": "550e8400-e29b-41d4-a716-446655440000",
        "start_time": "2024-01-20T09:00:00Z",
        "end_time": "2024-01-20T11:00:00Z",
        "status": "pending",
        "purpose": "Team standup meeting",
        "notes": "Weekly team sync with remote participants",
        "created_at": "2024-01-15T14:00:00Z",
        "updated_at": "2024-01-15T14:00:00Z"
    }
}

# Individual User Statistics
INDIVIDUAL_STATS_RESPONSE = {
    "status": "success",
    "message": "Individual user statistics retrieved successfully",
    "data": {
        "user_id": "550e8400-e29b-41d4-a716-446655440000",
        "user_type": "individual",
        "overview": {
            "total_bookings": 45,
            "active_bookings": 3,
            "completed_bookings": 38,
            "cancelled_bookings": 4,
            "organizations_joined": 2
        },
        "booking_stats": {
            "bookings_this_month": 8,
            "bookings_last_month": 12,
            "average_booking_duration": 120,
            "favorite_spaces": [
                {"space_name": "Quiet Desk 5", "bookings": 15},
                {"space_name": "Meeting Room B", "bookings": 8},
                {"space_name": "Phone Booth 2", "bookings": 6}
            ],
            "booking_patterns": {
                "preferred_times": {
                    "morning": 60,
                    "afternoon": 30,
                    "evening": 10
                },
                "preferred_days": {
                    "monday": 18,
                    "tuesday": 22,
                    "wednesday": 20,
                    "thursday": 25,
                    "friday": 15
                }
            }
        },
        "organization_memberships": [
            {
                "organization_id": "770fa622-g4bd-63f6-c938-668877662222",
                "organization_name": "TechCorp Solutions",
                "role": "member",
                "department": "Engineering",
                "joined_date": "2024-01-10T00:00:00Z"
            },
            {
                "organization_id": "881fb733-h5ce-74g7-d049-779988773333",
                "organization_name": "StartupHub",
                "role": "member",
                "department": None,
                "joined_date": "2024-01-05T00:00:00Z"
            }
        ],
        "period": "2024-01-15T00:00:00Z to 2024-01-15T23:59:59Z"
    }
}

# Error Examples for Different Scenarios
BOOKING_CONFLICT_ERROR = {
    "status": "error",
    "message": "Booking conflict detected",
    "errors": {
        "time_slot": ["The selected time slot conflicts with an existing booking"],
        "details": {
            "conflicting_booking_id": "aa1fc955-j7eg-96i9-f26b-99bb00995555",
            "conflict_start": "2024-01-20T09:30:00Z",
            "conflict_end": "2024-01-20T10:30:00Z"
        }
    }
}

SPACE_UNAVAILABLE_ERROR = {
    "status": "error",
    "message": "Space is not available for booking",
    "errors": {
        "space": ["Space is currently under maintenance"],
        "availability": {
            "status": "maintenance",
            "available_from": "2024-01-25T00:00:00Z",
            "alternative_spaces": [
                "bb2fd066-k8fh-07j0-g37c-00cc11006666",
                "cc3ge177-l9gi-18k1-h48d-11dd22117777"
            ]
        }
    }
}

PERMISSION_DENIED_ORGANIZATION = {
    "status": "error",
    "message": "Permission denied",
    "errors": {
        "permission": ["You do not have permission to perform this action in this organization"],
        "required_role": "admin",
        "current_role": "member",
        "organization_id": "770fa622-g4bd-63f6-c938-668877662222"
    }
}

# Location Examples
LOCATION_CREATE_REQUEST = {
    "name": "Downtown Office",
    "description": "Main office location in the city center",
    "address": "123 Business Street",
    "city": "San Francisco",
    "state": "California",
    "country": "United States",
    "postal_code": "94105",
    "timezone": "America/Los_Angeles",
    "opening_hours": {
        "monday": {"open": "08:00", "close": "18:00"},
        "tuesday": {"open": "08:00", "close": "18:00"},
        "wednesday": {"open": "08:00", "close": "18:00"},
        "thursday": {"open": "08:00", "close": "18:00"},
        "friday": {"open": "08:00", "close": "17:00"},
        "saturday": {"closed": True},
        "sunday": {"closed": True}
    }
}

LOCATION_SUCCESS_RESPONSE = {
    "status": "success",
    "message": "Location created successfully",
    "data": {
        "id": "dd4hf288-m0hj-29l2-i59e-22ee33228888",
        "name": "Downtown Office",
        "description": "Main office location in the city center",
        "address": "123 Business Street",
        "city": "San Francisco",
        "state": "California",
        "country": "United States",
        "postal_code": "94105",
        "timezone": "America/Los_Angeles",
        "status": "active",
        "opening_hours": {
            "monday": {"open": "08:00", "close": "18:00"},
            "tuesday": {"open": "08:00", "close": "18:00"},
            "wednesday": {"open": "08:00", "close": "18:00"},
            "thursday": {"open": "08:00", "close": "18:00"},
            "friday": {"open": "08:00", "close": "17:00"},
            "saturday": {"closed": True},
            "sunday": {"closed": True}
        },
        "created_at": "2024-01-15T15:00:00Z",
        "updated_at": "2024-01-15T15:00:00Z"
    }
}
