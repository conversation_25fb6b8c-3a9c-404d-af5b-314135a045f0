"""
Shared OpenAPI schemas for JechSpace API documentation.
This module contains reusable schema components for consistent API documentation.
"""

from drf_yasg import openapi

# Common response schemas
SUCCESS_RESPONSE_SCHEMA = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'status': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
        'message': openapi.Schema(type=openapi.TYPE_STRING, example='Operation completed successfully'),
        'data': openapi.Schema(type=openapi.TYPE_OBJECT, description='Response data'),
    },
    required=['status', 'message']
)

ERROR_RESPONSE_SCHEMA = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'status': openapi.Schema(type=openapi.TYPE_STRING, example='error'),
        'message': openapi.Schema(type=openapi.TYPE_STRING, example='Operation failed'),
        'errors': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            description='Detailed error information',
            additional_properties=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(type=openapi.TYPE_STRING)
            )
        ),
    },
    required=['status', 'message']
)

# Pagination schema
PAGINATION_SCHEMA = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'count': openapi.Schema(type=openapi.TYPE_INTEGER, example=100),
        'next': openapi.Schema(type=openapi.TYPE_STRING, format='uri', nullable=True),
        'previous': openapi.Schema(type=openapi.TYPE_STRING, format='uri', nullable=True),
        'results': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
    }
)

# User schema
USER_SCHEMA = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
        'email': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
        'first_name': openapi.Schema(type=openapi.TYPE_STRING),
        'last_name': openapi.Schema(type=openapi.TYPE_STRING),
        'phone_number': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'profession': openapi.Schema(type=openapi.TYPE_STRING),
        'bio': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'profile_picture': openapi.Schema(type=openapi.TYPE_STRING, format='uri', nullable=True),
        'is_verified': openapi.Schema(type=openapi.TYPE_BOOLEAN),
        'user_type': openapi.Schema(type=openapi.TYPE_STRING, enum=['individual', 'organization_owner']),
        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
        'updated_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
    }
)

# Organization schema
ORGANIZATION_SCHEMA = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
        'name': openapi.Schema(type=openapi.TYPE_STRING),
        'slug': openapi.Schema(type=openapi.TYPE_STRING),
        'description': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'type': openapi.Schema(type=openapi.TYPE_STRING, enum=['private', 'public']),
        'logo': openapi.Schema(type=openapi.TYPE_STRING, format='uri', nullable=True),
        'email': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
        'phone': openapi.Schema(type=openapi.TYPE_STRING),
        'website': openapi.Schema(type=openapi.TYPE_STRING, format='uri'),
        'employee_size': openapi.Schema(type=openapi.TYPE_STRING),
        'industry': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
        'updated_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
    }
)

# Location schema
LOCATION_SCHEMA = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
        'name': openapi.Schema(type=openapi.TYPE_STRING),
        'description': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'address': openapi.Schema(type=openapi.TYPE_STRING),
        'city': openapi.Schema(type=openapi.TYPE_STRING),
        'state': openapi.Schema(type=openapi.TYPE_STRING),
        'country': openapi.Schema(type=openapi.TYPE_STRING),
        'postal_code': openapi.Schema(type=openapi.TYPE_STRING),
        'timezone': openapi.Schema(type=openapi.TYPE_STRING),
        'status': openapi.Schema(type=openapi.TYPE_STRING, enum=['active', 'inactive']),
        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
        'updated_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
    }
)

# Space schema
SPACE_SCHEMA = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
        'name': openapi.Schema(type=openapi.TYPE_STRING),
        'slug': openapi.Schema(type=openapi.TYPE_STRING),
        'description': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'space_type': openapi.Schema(type=openapi.TYPE_STRING),
        'capacity': openapi.Schema(type=openapi.TYPE_INTEGER),
        'status': openapi.Schema(type=openapi.TYPE_STRING, enum=['available', 'occupied', 'maintenance']),
        'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN),
        'floor': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'room': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'desk': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
        'updated_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
    }
)

# Booking schema
BOOKING_SCHEMA = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
        'space': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
        'user': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
        'start_time': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
        'end_time': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
        'status': openapi.Schema(type=openapi.TYPE_STRING, enum=['pending', 'approved', 'rejected', 'cancelled']),
        'purpose': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'notes': openapi.Schema(type=openapi.TYPE_STRING, nullable=True),
        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
        'updated_at': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
    }
)

# Common error responses
VALIDATION_ERROR_RESPONSE = openapi.Response(
    description="Validation Error",
    schema=ERROR_RESPONSE_SCHEMA,
    examples={
        'application/json': {
            'status': 'error',
            'message': 'Validation Error',
            'errors': {
                'email': ['This field is required.'],
                'password': ['Password must be at least 8 characters long.']
            }
        }
    }
)

AUTHENTICATION_ERROR_RESPONSE = openapi.Response(
    description="Authentication Error",
    schema=ERROR_RESPONSE_SCHEMA,
    examples={
        'application/json': {
            'status': 'error',
            'message': 'Authentication failed',
            'errors': {
                'detail': ['Authentication credentials were not provided.']
            }
        }
    }
)

PERMISSION_ERROR_RESPONSE = openapi.Response(
    description="Permission Denied",
    schema=ERROR_RESPONSE_SCHEMA,
    examples={
        'application/json': {
            'status': 'error',
            'message': 'Permission denied',
            'errors': {
                'detail': ['You do not have permission to perform this action.']
            }
        }
    }
)

NOT_FOUND_ERROR_RESPONSE = openapi.Response(
    description="Resource Not Found",
    schema=ERROR_RESPONSE_SCHEMA,
    examples={
        'application/json': {
            'status': 'error',
            'message': 'Resource not found',
            'errors': {
                'detail': ['The requested resource was not found.']
            }
        }
    }
)

# Common parameters
ORGANIZATION_ID_PARAMETER = openapi.Parameter(
    'organization_id',
    openapi.IN_PATH,
    description="Organization UUID",
    type=openapi.TYPE_STRING,
    format='uuid',
    required=True
)

PAGE_PARAMETER = openapi.Parameter(
    'page',
    openapi.IN_QUERY,
    description="Page number for pagination",
    type=openapi.TYPE_INTEGER,
    default=1
)

PAGE_SIZE_PARAMETER = openapi.Parameter(
    'page_size',
    openapi.IN_QUERY,
    description="Number of items per page",
    type=openapi.TYPE_INTEGER,
    default=20
)

SEARCH_PARAMETER = openapi.Parameter(
    'search',
    openapi.IN_QUERY,
    description="Search query",
    type=openapi.TYPE_STRING
)

ORDERING_PARAMETER = openapi.Parameter(
    'ordering',
    openapi.IN_QUERY,
    description="Field to order by (prefix with '-' for descending)",
    type=openapi.TYPE_STRING
)
