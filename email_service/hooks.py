import logging

logger = logging.getLogger(__name__)

def email_sent_hook(success, result):
  """
  Hook called after an email job is processed
  Can be used for logging or additional actions

  Args:
      success: <PERSON><PERSON><PERSON> indicating if the email was sent successfully
      result: Result message or error details
  """
  if success:
    logger.info(f"Email job completed successfully: {result}")
  else:
    logger.error(f"Email job failed: {result}")
