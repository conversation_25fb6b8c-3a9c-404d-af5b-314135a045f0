from celery import shared_task
from django.db import models
from .models import EmailJob
from .services import BrevoEmailService


@shared_task(bind=True, max_retries=3, name='email_service.tasks.process_single_email_job')
def process_single_email_job(self, job_id):
  """Process a specific email job by ID"""
  try:
    job = EmailJob.objects.get(id=job_id)
  except EmailJob.DoesNotExist:
    return False, "Email job not found"

  email_service = BrevoEmailService()

  # Mark as processing
  job.mark_as_processing()

  try:
    # Get template ID and params from context data
    template_id = job.context_data.get('template_id')
    params = job.context_data.get('params', {})
    if not template_id:
        job.mark_as_failed("Template ID not provided in context data")
        return False, "Template ID not provided in context data"

    # Send the email using Brevo template
    success, response = email_service.send_template_email(
        to_email=job.recipient_email,
        template_id=template_id,
        params=params
    )

    if success:
      job.mark_as_sent()
      # Call email_sent_hook logic directly
      from .hooks import email_sent_hook
      email_sent_hook(True, "Em<PERSON> sent successfully")
      return True, "<PERSON><PERSON> sent successfully"
    else:
      job.mark_as_failed(str(response))
      # Call email_sent_hook logic directly
      from .hooks import email_sent_hook
      email_sent_hook(False, str(response))
      return False, str(response)

  except Exception as e:
    # Handle any exceptions
    job.mark_as_failed(str(e))
    # Retry the task if we haven't exceeded the max retries
    if job.retry_count < job.max_retries:
      self.retry(exc=e, countdown=60 * 2 ** job.retry_count)  # Exponential backoff
    
    # Call email_sent_hook logic directly
    from .hooks import email_sent_hook
    email_sent_hook(False, str(e))
    return False, str(e)


@shared_task(name='email_service.tasks.process_email_jobs')
def process_email_jobs():
  """
  Process pending email jobs
  This function is called by the task scheduler
  """
  # Get pending jobs that haven't exceeded max_retries
  pending_jobs = EmailJob.objects.filter(
    status__in=['pending', 'failed'],
  ).exclude(
    retry_count__gte=models.F('max_retries')
    )

  for job in pending_jobs:
    process_single_email_job.delay(job.id)
