from django.conf import settings

from events.constants import EventTypes
from events.events import events

from .services import EmailJobService, EmailTypes
from .tasks import process_single_email_job
from .templates import BrevoTemplates


@events.on(EventTypes.SEND_VERIFICATION_CODE)
def handle_user_created(payload):
    """
    Handler for user.created events
    Creates and queues a verification email job

    Args:
        payload: Dictionary containing user data and token
    """
    if not payload.get("user") or not payload.get("token"):
        return

    user = payload["user"]
    token = payload["token"]

    if user.is_verified:
        return

    # Create context data for Brevo template
    context_data = {
        "template_id": BrevoTemplates.verification,
        "params": {
            "email": user.email,
            "first_name": user.first_name,
            "verification_link": f"{settings.CLIENT_URL}/verify-email?token={token}",
        },
    }

    # Create email job
    job = EmailJobService.create_email_job(
        email_type=EmailTypes.VERIFICATION, recipient_email=user.email, context_data=context_data
    )

    # Queue the email job for processing using Celery
    process_single_email_job.delay(job.id)


@events.on(EventTypes.SEND_PASSWORD_RESET_CODE)
def handle_password_reset_requested(payload):
    """
    Handler for password reset email
    Args:
        payload: Dictionary containing user data and token
    """
    if not payload.get("user") or not payload.get("token"):
        return

    user = payload["user"]
    token = payload["token"]

    # Create context data for Brevo template
    context_data = {
        "template_id": BrevoTemplates.password_reset,
        "params": {
            "email": user.email,
            "first_name": user.first_name,
            "reset_link": f"{settings.CLIENT_URL}/reset-password?token={token}",
        },
    }

    # Create email job
    job = EmailJobService.create_email_job(
        email_type=EmailTypes.PASSWORD_RESET, recipient_email=user.email, context_data=context_data
    )

    # Queue the email job for processing using Celery
    process_single_email_job.delay(job.id)


@events.on(EventTypes.SEND_USER_INVITATION)
def handle_user_invited(payload):
    """
    Handler for user invitation email

    Args:
        payload: Dict with user email, role, and token
    """
    if not payload.get("email") or not payload.get("role") or not payload.get("token"):
        return

    email = payload["email"]
    role = payload["role"]
    token = payload["token"]

    context_data = {
        "template_id": BrevoTemplates.user_invitation,
        "params": {
            "role": role,
            "invitation_link": f"{settings.CLIENT_URL}/invite?token={token}",
        },
    }

    job = EmailJobService.create_email_job(
        email_type="user_invitation", recipient_email=email, context_data=context_data
    )

    process_single_email_job.delay(job.id)


@events.on(EventTypes.SEND_BOOKING_CREATED)
def handle_booking_created(payload):
    if (
        not payload.get("email")
        or not payload.get("name")
        or not payload.get("location")
    ):
        return

    context_data = {
        "template_id": BrevoTemplates.booking_created,
        "params": {
            "name": payload["name"],
            "location": payload["location"],
            "start_time": payload["start_time"],
            "end_time": payload["end_time"],
        },
    }

    job = EmailJobService.create_email_job(
        email_type="booking_created",
        recipient_email=payload["email"],
        context_data=context_data,
    )
    process_single_email_job.delay(job.id)


@events.on(EventTypes.SEND_BOOKING_EXTENDED)
def handle_booking_extended(payload):
    if (
        not payload.get("email")
        or not payload.get("name")
        or not payload.get("location")
    ):
        return

    context_data = {
        "template_id": BrevoTemplates.booking_extended,
        "params": {
            "name": payload["name"],
            "location": payload["location"],
            "new_end_time": payload["new_end_time"],
        },
    }

    job = EmailJobService.create_email_job(
        email_type="booking_extended",
        recipient_email=payload["email"],
        context_data=context_data,
    )
    process_single_email_job.delay(job.id)


@events.on(EventTypes.SEND_BOOKING_CANCELLED)
def handle_booking_cancelled(payload):
    if (
        not payload.get("email")
        or not payload.get("name")
        or not payload.get("location")
    ):
        return

    context_data = {
        "template_id": BrevoTemplates.booking_cancelled,
        "params": {
            "name": payload["name"],
            "location": payload["location"],
        },
    }

    job = EmailJobService.create_email_job(
        email_type="booking_cancelled",
        recipient_email=payload["email"],
        context_data=context_data,
    )
    process_single_email_job.delay(job.id)
