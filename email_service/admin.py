from django.contrib import admin
from .models import <PERSON><PERSON><PERSON><PERSON>
from .tasks import process_single_email_job


@admin.action(description="Retry sending selected emails")
def retry_emails(modeladmin, request, queryset):
  for job in queryset:
    process_single_email_job.delay(job.id)

class EmailJobAdmin(admin.ModelAdmin):
  list_display = ('id', 'email_type', 'recipient_email', 'status', 'retry_count', 'created_at', 'processed_at')
  list_filter = ('status', 'email_type', 'created_at')
  search_fields = ('recipient_email', 'error_message')
  readonly_fields = ('id', 'created_at', 'updated_at', 'processed_at')
  actions = [retry_emails]
  
  fieldsets = (
    (None, {
        'fields': ('id', 'email_type', 'recipient_email', 'status')
    }),
    ('Data', {
        'fields': ('context_data',)
    }),
    ('Processing Details', {
        'fields': ('retry_count', 'max_retries', 'error_message')
    }),
    ('Timestamps', {
        'fields': ('created_at', 'updated_at', 'processed_at')
    }),
  )

admin.site.register(EmailJob, EmailJobAdmin)