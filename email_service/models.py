from django.db import models
from django.utils import timezone

from core.models import BaseModel


class EmailJob(BaseModel):
    """
    Model to store email jobs that need to be processed
    """

    STATUS_CHOICES = (
        ("pending", "Pending"),
        ("processing", "Processing"),
        ("sent", "Sent"),
        ("failed", "Failed"),
    )

    email_type = models.CharField(
        max_length=50
    )  # e.g., 'verification', 'reset_password'
    recipient_email = models.EmailField()

    # JSON serialized data needed for the email
    context_data = models.JSONField(default=dict)

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="pending")
    error_message = models.TextField(blank=True, null=True)

    # Track attempts and timing
    retry_count = models.IntegerField(default=0)
    max_retries = models.IntegerField(default=3)
    processed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.email_type} to {self.recipient_email} - {self.status}"

    def mark_as_processing(self):
        self.status = "processing"
        self.save(update_fields=["status", "updated_at"])

    def mark_as_sent(self):
        self.status = "sent"
        self.processed_at = timezone.now()
        self.save(update_fields=["status", "processed_at", "updated_at"])

    def mark_as_failed(self, error_message):
        self.status = "failed"
        self.error_message = error_message
        self.retry_count += 1
        self.save(
            update_fields=["status", "error_message", "retry_count", "updated_at"]
        )
