import sib_api_v3_sdk
from sib_api_v3_sdk.rest import ApiException
from django.conf import settings
from .models import EmailJob

class EmailTypes:
  VERIFICATION = "verification"
  PASSWORD_RESET = "password_reset"
  USER_INVITATION = "user_invitation"
  BOOKING_CREATED = "booking_created"
  BOOKING_EXTENDED = "booking_extended"
  BOOKING_CANCELLED = "booking_cancelled"

class BrevoEmailService:
  """Service for sending emails via Brevo API using their template system"""

  def __init__(self):
    # Configure API key authorization
    self.configuration = sib_api_v3_sdk.Configuration()
    self.configuration.api_key['api-key'] = settings.BREVO_API_KEY

    # Create an instance of the API class
    self.api_instance = sib_api_v3_sdk.TransactionalEmailsApi(
      sib_api_v3_sdk.ApiClient(self.configuration)
    )

  def send_template_email(self, to_email, template_id, params):
    """
    Send an email using a Brevo template

    Args:
      to_email: Recipient email address
      template_id: ID of the Brevo template to use
      params: Dictionary of parameters to pass to the template
    """
    sender = {"email": "<EMAIL>", "name": "Jechies Support"}
    to = [{"email": to_email}]

    email = sib_api_v3_sdk.SendSmtpEmail(
      to=to,
      sender=sender,
      template_id=template_id,
      params=params
    )

    try:
      api_response = self.api_instance.send_transac_email(email)
      return True, api_response
    except ApiException as e:
      return False, str(e)


class EmailJobService:
  """Service for creating and managing email jobs"""

  @staticmethod
  def create_email_job(email_type, recipient_email, context_data):
    """Create a job for sending emails"""

    # Create email job
    job = EmailJob.objects.create(
      email_type=email_type,
      recipient_email=recipient_email,
      context_data=context_data
    )

    return job
