# Generated by Django 5.2 on 2025-06-03 12:56

import uuid

import django.db.models.deletion
from django.db import migrations, models

import invitations.models


class Migration(migrations.Migration):
    initial = True

    dependencies = [
        ("departments", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Invitation",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("email", models.EmailField(max_length=254)),
                (
                    "role",
                    models.CharField(
                        choices=[
                            ("admin", "Admin"),
                            ("owner", "Owner"),
                            ("employee", "Employee"),
                            ("member", "Member"),
                        ],
                        default="member",
                        max_length=10,
                    ),
                ),
                ("message", models.TextField(blank=True, null=True)),
                (
                    "status",
                    models.Char<PERSON>ield(
                        choices=[
                            ("pending", "Pending"),
                            ("accepted", "Accepted"),
                            ("declined", "Declined"),
                            ("expired", "Expired"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=10,
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(default=invitations.models.get_expiry_date),
                ),
                (
                    "department",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="invitations",
                        to="departments.department",
                    ),
                ),
            ],
            options={
                "abstract": False,
            },
        ),
    ]
