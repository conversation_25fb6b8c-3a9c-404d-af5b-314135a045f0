from email_validator import EmailNotValidError, validate_email

from users.models import Roles


class InvitationValidator:
    """Validator for invitation data"""

    # Valid roles that can be assigned to users
    VALID_ROLES = [Roles.ADMIN, Roles.EMPLOYEE, Roles.MEMBER]

    @staticmethod
    def validate_email_format(email):
        """Validate email format"""
        try:
            validate_email(email)
            return True, None
        except EmailNotValidError as e:
            return False, str(e)

    @staticmethod
    def validate_role(role):
        """Validate that a role is acceptable"""
        if not role or role.lower() not in InvitationValidator.VALID_ROLES:
            return (
                False,
                f"Role must be one of: {', '.join(InvitationValidator.VALID_ROLES)}",
            )
        return True, None
