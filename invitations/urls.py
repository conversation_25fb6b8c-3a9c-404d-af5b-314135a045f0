from django.urls import path

from .views import (
    BulkInvitationUploadView,
    InvitationDetailView,
    InvitationResponseView,
    OrganizationInvitationsView,
    UserInvitationsView,
)

app_name = "invitations"

urlpatterns = [
    # Organization invitation endpoints
    path(
        "organizations/<uuid:organization_id>/invitations/",
        OrganizationInvitationsView.as_view(),
        name="organization_invitations",
    ),
    path(
        "organizations/<uuid:organization_id>/invitations/bulk/",
        BulkInvitationUploadView.as_view(),
        name="bulk_invitation_upload",
    ),
    path(
        "organizations/<uuid:organization_id>/invitations/<uuid:invitation_id>/",
        InvitationDetailView.as_view(),
        name="invitation_detail",
    ),
    # User invitation endpoints
    path(
        "invitations/",
        UserInvitationsView.as_view(),
        name="user_invitations",
    ),
    path(
        "invitations/<uuid:invitation_id>/",
        InvitationResponseView.as_view(),
        name="respond_to_invitation",
    ),
]
