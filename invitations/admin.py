from django.contrib import admin

from .models import Invitation


class InvitationAdmin(admin.ModelAdmin):
    list_display = (
        "email",
        "role",
        "status",
        "organization",
        "invited_by",
        "created_at",
        "expires_at",
    )
    search_fields = ("email", "invited_by__email", "organization__name")
    list_filter = ("status", "organization", "role", "created_at")


admin.site.register(Invitation, InvitationAdmin)
