from datetime import timedelta

from django.utils import timezone

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.parsers import FormParser, MultiPartParser
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView

from core.pagination import CustomPageNumberPagination
from departments.models import Department
from events.constants import EventTypes
from events.events import events
from jechspace_backend.utils import ErrorCodes, api_response
from organizations.mixins import OrganizationPermissionMixin
from organizations.models import Organization
from permissions.constants import PermissionActions, PermissionCategories
from users.models import User, UserOrganization, UserToken

from .models import Invitation, InvitationStatus
from .serializers import (
    BulkInvitationUploadSerializer,
    InvitationSerializer,
    InvitationUpdateSerializer,
)
from .services import InvitationService


class OrganizationInvitationsView(OrganizationPermissionMixin, APIView):
    """
    API view for creating and listing organization invitations
    """

    permission_category = PermissionCategories.INVITATION
    permission_action = PermissionActions.CREATE
    pagination_class = CustomPageNumberPagination
    ordering_fields = ["created_at", "expires_at", "email", "status"]
    ordering = ["-created_at"]

    @swagger_auto_schema(
        operation_summary="Send invitation",
        operation_description="Sends an invitation to join the organization",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["email", "role"],
            properties={
                "email": openapi.Schema(type=openapi.TYPE_STRING, format="email"),
                "role": openapi.Schema(
                    type=openapi.TYPE_STRING, enum=["admin", "employee", "member"]
                ),
                "department_id": openapi.Schema(type=openapi.TYPE_STRING),
                "message": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
        responses={
            201: openapi.Response(description="Invitation sent successfully"),
            400: openapi.Response(description="Invalid data"),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Invitations"],
    )
    def post(self, request, organization_id):
        """
        Send an invitation to join the organization
        """
        # Organization is already validated and available as request.organization
        organization = request.organization
        user = request.user
        request.data["organization"] = organization.id

        serializer = InvitationSerializer(
            data=request.data,
            context={"request": request, "organization": organization},
        )

        if serializer.is_valid():
            invitation = serializer.save()

            # events.emit(
            #     EventTypes.SEND_USER_INVITATION,
            #     { "token": invitation.id, "email": serializer.validated_data.get("email"), "role": invitation.role }
            # )

            return api_response(
                data=serializer.data,
                message="Invitation sent successfully",
                status_code=status.HTTP_201_CREATED,
            )

        return api_response(
            errors=serializer.errors,
            message="Validation error",
            status_code=status.HTTP_400_BAD_REQUEST,
            success=False,
        )

    @swagger_auto_schema(
        operation_summary="List sent invitations",
        operation_description="Lists all invitations sent by the organization",
        manual_parameters=[
            openapi.Parameter(
                "page",
                openapi.IN_QUERY,
                description="Page number",
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                "limit",
                openapi.IN_QUERY,
                description="Number of items per page",
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                "status",
                openapi.IN_QUERY,
                description="Filter by status",
                type=openapi.TYPE_STRING,
                enum=["pending", "accepted", "declined", "expired"],
            ),
            openapi.Parameter(
                "ordering",
                openapi.IN_QUERY,
                description="Order by field (prefix with - for descending)",
                type=openapi.TYPE_STRING,
                enum=[
                    "created_at",
                    "-created_at",
                    "expires_at",
                    "-expires_at",
                    "email",
                    "-email",
                    "status",
                    "-status",
                ],
            ),
        ],
        responses={
            200: openapi.Response(description="Invitations retrieved successfully"),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Invitations"],
    )
    def get(self, request, organization_id):
        """
        List all invitations sent by the organization
        """
        self.permission_action = PermissionActions.MANAGE
        # Organization is already validated and available as request.organization
        organization = request.organization

        # Get query parameters
        status_filter = request.query_params.get("status")

        # Base queryset
        invitations = Invitation.objects.filter(organization=organization)

        # Apply filters
        if status_filter:
            invitations = invitations.filter(status=status_filter)
        else:
            invitations = invitations.exclude(status__in=[InvitationStatus.CANCELLED])

        # Apply pagination
        paginator = self.pagination_class()
        paginated_invitations = paginator.paginate_queryset(invitations, request, self)

        # Serialize the data
        serializer = InvitationSerializer(
            paginated_invitations,
            many=True,
            context={"request": request, "organization": organization},
        )

        return paginator.get_paginated_response(
            serializer.data, message="Invitations retrieved successfully"
        )


class BulkInvitationUploadView(OrganizationPermissionMixin, APIView):
    """API view for handling bulk user invitations via CSV upload"""

    permission_category = PermissionCategories.INVITATION
    permission_action = PermissionActions.CREATE
    parser_classes = (MultiPartParser, FormParser)

    @swagger_auto_schema(
        operation_summary="Upload bulk invitations",
        operation_description="Upload a CSV file to send multiple invitations",
        consumes=['multipart/form-data'],
        manual_parameters=[
            openapi.Parameter(
                "file",
                openapi.IN_FORM,
                description="CSV file containing invitation details",
                type=openapi.TYPE_FILE,
                required=True,
            ),
        ],
        responses={
            201: openapi.Response(
                description="Bulk invitations processed successfully"
            ),
            400: openapi.Response(description="Invalid file format"),
            403: openapi.Response(description="Permission denied"),
        },
        tags=["Invitations"],
    )
    def post(self, request, organization_id):
        """Handle POST request with CSV file upload"""
        serializer = BulkInvitationUploadSerializer(data=request.FILES)
        if not serializer.is_valid():
            return api_response(
                success=False,
                message="Invalid file upload",
                errors=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST,
            )

        # Initialize service
        invitation_service = InvitationService()

        # Organization is already validated by the permission mixin
        organization = request.organization

        # Process the file
        result = invitation_service.process_csv_invitations(
            serializer.validated_data["file"], organization_id, request.user
        )

        # Return appropriate response
        return api_response(
            success=result.get("success", True),
            message="Bulk invitations processed"
            if result.get("success")
            else result.get("message"),
            data=result.get("data", {}),
            errors=result.get("errors", None),
            status_code=result.get("status_code", status.HTTP_201_CREATED),
        )


class InvitationDetailView(OrganizationPermissionMixin, APIView):
    """
    API view for managing a specific invitation
    """

    permission_category = PermissionCategories.INVITATION
    permission_action = PermissionActions.MANAGE

    @swagger_auto_schema(
        operation_summary="Get invitation details",
        operation_description="Get details of a specific invitation",
        responses={
            200: openapi.Response(
                description="Invitation details retrieved successfully"
            ),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Invitation not found"),
        },
        tags=["Invitations"],
    )
    def get(self, request, organization_id, invitation_id):
        """
        Get invitation details
        """
        # Organization is already validated by the permission mixin
        organization = request.organization

        try:
            invitation = Invitation.objects.get(
                id=invitation_id, organization=organization
            )
            serializer = InvitationSerializer(invitation)
            return api_response(
                data=serializer.data,
                message="Invitation details retrieved successfully",
                status_code=status.HTTP_200_OK,
            )
        except Invitation.DoesNotExist:
            return api_response(
                message="Invitation not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "invitation": {
                        "code": ErrorCodes.INVITATION_NOT_FOUND,
                        "message": "Invitation not found",
                    }
                },
            )

    @swagger_auto_schema(
        operation_summary="Cancel invitation",
        operation_description="Cancel a pending invitation",
        responses={
            200: openapi.Response(description="Invitation cancelled successfully"),
            400: openapi.Response(description="Invitation cannot be cancelled"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Invitation not found"),
        },
        tags=["Invitations"],
    )
    def delete(self, request, organization_id, invitation_id):
        """
        Cancel an invitation
        """
        # Organization is already validated by the permission mixin
        organization = request.organization

        try:
            invitation = Invitation.objects.get(
                id=invitation_id, organization=organization
            )

            # Only pending invitations can be cancelled
            if invitation.status not in [
                InvitationStatus.PENDING,
                InvitationStatus.EXPIRED,
            ]:
                return api_response(
                    message="Only pending invitations can be cancelled",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={
                        "invitation": {
                            "code": ErrorCodes.INVITATION_ALREADY_PROCESSED,
                            "message": "This invitation has already been processed",
                        }
                    },
                )

            invitation.status = InvitationStatus.CANCELLED
            invitation.save()

            return api_response(
                message="Invitation cancelled successfully",
                status_code=status.HTTP_200_OK,
            )
        except Invitation.DoesNotExist:
            return api_response(
                message="Invitation not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "invitation": {
                        "code": ErrorCodes.INVITATION_NOT_FOUND,
                        "message": "Invitation not found",
                    }
                },
            )

    @swagger_auto_schema(
        operation_summary="Resend invitation",
        operation_description="Resend an invitation email",
        responses={
            200: openapi.Response(description="Invitation resent successfully"),
            400: openapi.Response(description="Invitation cannot be resent"),
            403: openapi.Response(description="Permission denied"),
            404: openapi.Response(description="Invitation not found"),
        },
        tags=["Invitations"],
    )
    def post(self, request, organization_id, invitation_id):
        """
        Resend an invitation
        """
        # Organization is already validated by the permission mixin
        organization = request.organization

        try:
            invitation = Invitation.objects.get(
                id=invitation_id, organization=organization
            )

            # Only pending invitations can be resent
            if invitation.status not in [
                InvitationStatus.PENDING,
                InvitationStatus.EXPIRED,
            ]:
                return api_response(
                    message="Only pending or expired invitations can be resent",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={
                        "invitation": {
                            "code": ErrorCodes.INVITATION_ALREADY_PROCESSED,
                            "message": "This invitation has already been processed",
                        }
                    },
                )

            # Reset expiration date
            invitation.expires_at = timezone.now() + timedelta(days=7)
            invitation.save()

            # events.emit(
            #     EventTypes.SEND_USER_INVITATION,
            #     { "token": invitation.id, "email": invitation.email, "role": invitation.role }
            # )

            return api_response(
                message="Invitation resent successfully",
                data=InvitationSerializer(invitation).data,
                status_code=status.HTTP_200_OK,
            )
        except Invitation.DoesNotExist:
            return api_response(
                message="Invitation not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "invitation": {
                        "code": ErrorCodes.INVITATION_NOT_FOUND,
                        "message": "Invitation not found",
                    }
                },
            )


class UserInvitationsView(APIView):
    """
    API view for listing user's received invitations
    """

    permission_classes = [IsAuthenticated]
    pagination_class = CustomPageNumberPagination
    ordering_fields = ["created_at", "expires_at", "status"]
    ordering = ["-created_at"]

    @swagger_auto_schema(
        operation_summary="List received invitations",
        operation_description="Lists all invitations received by the current user",
        manual_parameters=[
            openapi.Parameter(
                "page",
                openapi.IN_QUERY,
                description="Page number",
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                "limit",
                openapi.IN_QUERY,
                description="Number of items per page",
                type=openapi.TYPE_INTEGER,
            ),
            openapi.Parameter(
                "status",
                openapi.IN_QUERY,
                description="Filter by status",
                type=openapi.TYPE_STRING,
                enum=["pending", "accepted", "declined", "expired"],
            ),
            openapi.Parameter(
                "ordering",
                openapi.IN_QUERY,
                description="Order by field (prefix with - for descending)",
                type=openapi.TYPE_STRING,
                enum=[
                    "created_at",
                    "-created_at",
                    "expires_at",
                    "-expires_at",
                    "status",
                    "-status",
                ],
            ),
        ],
        responses={
            200: openapi.Response(description="Invitations retrieved successfully"),
        },
        tags=["Invitations"],
    )
    def get(self, request):
        """
        List all invitations received by the current user
        """
        # Get query parameters
        status_filter = request.query_params.get("status")

        # Base queryset
        invitations = Invitation.objects.filter(email=request.user.email)

        # Apply filters
        if status_filter:
            invitations = invitations.filter(status=status_filter)
        else:
            invitations = invitations.exclude(status__in=[InvitationStatus.CANCELLED])

        # Apply pagination
        paginator = self.pagination_class()
        paginated_invitations = paginator.paginate_queryset(invitations, request, self)

        # Serialize the data
        serializer = InvitationSerializer(
            paginated_invitations, many=True, context={"request": request}
        )

        return paginator.get_paginated_response(
            serializer.data, message="Invitations retrieved successfully"
        )


class InvitationResponseView(APIView):
    """
    API view for responding to invitations (accept or decline)
    """

    permission_classes = [IsAuthenticated]

    def get(self, request, invitation_id):
        """
        Get an invitation details
        """

        try:
            invitation = Invitation.objects.get(id=invitation_id)
            if invitation.email.lower() != request.user.email.lower():
                return api_response(
                    message="This invitation is not for you",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={
                        "invitation": {
                            "code": ErrorCodes.INVITATION_NOT_FOR_USER,
                            "message": "This invitation is not for you",
                        }
                    },
                )

            serializer = InvitationSerializer(invitation)
            return api_response(
                data=serializer.data,
                message="Invitation details retrieved successfully",
                status_code=status.HTTP_200_OK,
            )
        except Invitation.DoesNotExist:
            return api_response(
                message="Invitation not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "invitation": {
                        "code": ErrorCodes.INVITATION_NOT_FOUND,
                        "message": "Invitation not found",
                    }
                },
            )

    @swagger_auto_schema(
        operation_summary="Respond to invitation",
        operation_description="Accept or decline an invitation to join an organization",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["status"],
            properties={
                "status": openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=[InvitationStatus.ACCEPTED, InvitationStatus.DECLINED],
                ),
            },
        ),
        responses={
            200: openapi.Response(
                description="Invitation response processed successfully"
            ),
            400: openapi.Response(description="Invalid request"),
            404: openapi.Response(description="Invitation not found"),
        },
        tags=["Invitations"],
    )
    def patch(self, request, invitation_id):
        """
        Respond to an invitation (accept or decline)
        """
        try:
            invitation = Invitation.objects.get(id=invitation_id)

            # Verify the invitation is for this user (this is a security check that belongs in the view)
            if invitation.email.lower() != request.user.email.lower():
                return api_response(
                    message="This invitation is not for you",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={
                        "invitation": {
                            "code": ErrorCodes.INVITATION_NOT_FOR_USER,
                            "message": "This invitation is not for you",
                        }
                    },
                )

            if invitation.status == InvitationStatus.CANCELLED:
                return api_response(
                    message="This invitation was cancelled",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={
                        "invitation": {
                            "code": ErrorCodes.INVITATION_CANCELLED,
                            "message": "This invitation was cancelled",
                        }
                    },
                )

            # Check if invitation has expired before validation
            if (
                invitation.status == InvitationStatus.PENDING
                and invitation.expires_at < timezone.now()
            ):
                # TODO: Create a cron job to update invitation status
                invitation.status = InvitationStatus.EXPIRED
                invitation.save()
                return api_response(
                    message="This invitation has expired",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors={
                        "invitation": {
                            "code": ErrorCodes.INVITATION_EXPIRED,
                            "message": "This invitation has expired",
                        }
                    },
                )

            # Use serializer for data validation
            serializer = InvitationUpdateSerializer(
                invitation, data=request.data, context={"request": request}
            )
            if not serializer.is_valid():
                return api_response(
                    message="Invalid request",
                    status_code=status.HTTP_400_BAD_REQUEST,
                    success=False,
                    errors=serializer.errors,
                )

            # Process the response based on status
            new_status = serializer.validated_data["status"]

            if new_status == InvitationStatus.ACCEPTED:
                # Create user-organization relationship
                UserOrganization.objects.create(
                    user=request.user,
                    organization=invitation.organization,
                    role=invitation.role,
                    department=invitation.department,
                )

                # Save the updated status
                serializer.save()

                return api_response(
                    message="Invitation accepted successfully",
                    data={
                        "user": {
                            "id": str(request.user.id),
                            "first_name": request.user.first_name,
                            "last_name": request.user.last_name,
                            "role": invitation.role,
                        },
                        "organization": {
                            "id": str(invitation.organization.id),
                            "name": invitation.organization.name,
                            "slug": invitation.organization.slug,
                            "logo": invitation.organization.logo.url
                            if invitation.organization.logo
                            else None,
                        },
                    },
                    status_code=status.HTTP_200_OK,
                )
            elif new_status == InvitationStatus.DECLINED:
                # Save the updated status
                serializer.save()

                return api_response(
                    message="Invitation declined successfully",
                    status_code=status.HTTP_200_OK,
                )

        except Invitation.DoesNotExist:
            return api_response(
                message="Invitation not found",
                status_code=status.HTTP_404_NOT_FOUND,
                success=False,
                errors={
                    "invitation": {
                        "code": ErrorCodes.INVITATION_NOT_FOUND,
                        "message": "Invitation not found",
                    }
                },
            )
