from django.utils import timezone

from rest_framework import serializers

from departments.models import Department
from jechspace_backend.utils import ErrorCodes
from organizations.models import Organization
from users.models import Roles, User, UserOrganization

from .models import Invitation, InvitationStatus


class DepartmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Department
        fields = ["id", "name"]


class UserSerializer(serializers.ModelSerializer):
    role = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = User
        fields = ["id", "email", "first_name", "last_name", "role"]

    def get_role(self, obj):
        # Get the organization from parent object (Invitation) if available
        invitation = self.context.get("invitation")
        organization = None

        if invitation:
            organization = invitation.organization
        else:
            organization = self.context.get("organization")

        if not organization:
            return None

        try:
            user_organization = UserOrganization.objects.get(
                user=obj, organization=organization
            )
            return user_organization.role
        except UserOrganization.DoesNotExist:
            return None


class OrganizationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = ["id", "name", "slug", "description", "type", "logo"]


class InvitationSerializer(serializers.ModelSerializer):
    """
    Serializer for the Invitation model
    """

    inviter = serializers.SerializerMethodField(read_only=True)
    organization = OrganizationSerializer(read_only=True)
    department_field = serializers.PrimaryKeyRelatedField(
        queryset=Department.objects.all(),
        write_only=True,
        required=False,
        allow_null=True,
    )
    department = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = Invitation
        fields = [
            "id",
            "email",
            "role",
            "department_field",
            "department",
            "message",
            "status",
            "inviter",
            "organization",
            "created_at",
            "updated_at",
            "expires_at",
        ]
        read_only_fields = [
            "id",
            "status",
            "inviter",
            "created_at",
            "updated_at",
            "expires_at",
            "organization",
            "department",
        ]
        extra_kwargs = {
            "department": {"required": False, "write_only": True},
            "role": {"required": True, "write_only": True},
        }

    def get_inviter(self, obj):
        return UserSerializer(
            obj.invited_by,
            context={"invitation": obj, "organization": obj.organization},
        ).data

    def get_department(self, obj):
        if obj.department:
            return DepartmentSerializer(obj.department).data
        return None

    def validate(self, data):
        """
        Validate that department is provided when role is Employee or Admin
        """
        role = data.get("role")
        department = data.get("department_field")

        if role in [Roles.ADMIN, Roles.EMPLOYEE] and not department:
            raise serializers.ValidationError(
                {
                    "department": {
                        "code": ErrorCodes.DEPARTMENT_REQUIRED,
                        "message": "Department is required when role is an employee or admin",
                    }
                }
            )

        return data

    def validate_role(self, value):
        """
        Validate that role is not Owner
        """
        if value not in [Roles.ADMIN, Roles.EMPLOYEE, Roles.OWNER]:
            raise serializers.ValidationError(
                {
                    "role": {
                        "code": ErrorCodes.INVALID_ROLE,
                        "message": "Invalid role",
                    }
                }
            )

        if value == Roles.OWNER:
            raise serializers.ValidationError(
                {
                    "role": {
                        "code": ErrorCodes.CANNOT_INVITE_OWNER,
                        "message": "Owner role is not allowed to be invited",
                    }
                }
            )

        return value

    def validate_email(self, value):
        """
        Ensure the email is not already invited or already a member.
        """
        organization = self.context.get("organization")

        # Check if a pending invitation already exists
        if Invitation.objects.filter(
            email=value, organization=organization, status=InvitationStatus.PENDING
        ).exists():
            raise serializers.ValidationError(
                "This email has already been invited.",
                code=ErrorCodes.INVITATION_ALREADY_SENT,
            )

        # Check if a user with this email already exists in this organization
        try:
            user = User.objects.get(email=value)
            if user.organizations.filter(organization=organization).exists():
                raise serializers.ValidationError(
                    "This email is already a member of this organization.",
                    code=ErrorCodes.INVITATION_USER_ALREADY_MEMBER,
                )
        except User.DoesNotExist:
            pass

        return value

    def create(self, validated_data):
        """
        Create a new invitation.
        """
        request = self.context.get("request")
        organization = self.context.get("organization")

        # Rename department_field to department
        if "department_field" in validated_data:
            validated_data["department"] = validated_data.pop("department_field")

        # Ensure organization is directly assigned
        validated_data["organization"] = organization
        validated_data["invited_by"] = request.user
        return super().create(validated_data)


class BulkInvitationUploadSerializer(serializers.Serializer):
    file = serializers.FileField()

    def validate_file(self, value):
        # Validate file type is CSV
        if not value.name.endswith(".csv"):
            raise serializers.ValidationError("Only CSV files are accepted.")
        return value


class InvitationUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for responding to invitations (accept or decline)
    """

    status = serializers.ChoiceField(choices=InvitationStatus.CHOICES)

    class Meta:
        model = Invitation
        fields = ["status"]

    def validate_status(self, value):
        """Validate the status transition"""
        # Only allow specific status values for user responses
        if value not in [InvitationStatus.ACCEPTED, InvitationStatus.DECLINED]:
            raise serializers.ValidationError(
                f"Status must be one of: {InvitationStatus.ACCEPTED}, {InvitationStatus.DECLINED}",
                code=ErrorCodes.INVALID_STATUS,
            )

        # Check if invitation is in pending state
        if self.instance.status != InvitationStatus.PENDING:
            raise serializers.ValidationError(
                "Only pending invitations can be updated",
                code=ErrorCodes.INVITATION_ALREADY_PROCESSED,
            )

        # Check if invitation has expired
        if self.instance.expires_at < timezone.now():
            self.instance.status = InvitationStatus.EXPIRED
            self.instance.save(update_fields=["status"])
            raise serializers.ValidationError(
                "This invitation has expired",
                code=ErrorCodes.INVITATION_EXPIRED,
            )

        return value
