from datetime import timedelta

from django.conf import settings
from django.core.exceptions import ValidationError
from django.db import models
from django.utils import timezone

from core.models import BaseModel
from organizations.models import Organization
from users.models import ROLE_CHOICES, Roles


class InvitationStatus:
    PENDING = "pending"
    ACCEPTED = "accepted"
    DECLINED = "declined"
    EXPIRED = "expired"
    CANCELLED = "cancelled"

    CHOICES = (
        (PENDING, "Pending"),
        (ACCEPTED, "Accepted"),
        (DECLINED, "Declined"),
        (EXPIRED, "Expired"),
        (CANCELLED, "Cancelled"),
    )


def get_expiry_date():
    """Returns a date 7 days from now"""
    return timezone.now() + timedelta(days=7)


class Invitation(BaseModel):
    """
    Invitation model for inviting users to join an organization.
    """

    # Email address that is being invited.
    email = models.EmailField()

    # Role assigned to the invited user.
    role = models.CharField(max_length=10, choices=ROLE_CHOICES, default=Roles.MEMBER)

    # Department the user will belong to (if role is Employee)
    department = models.ForeignKey(
        "departments.Department",
        on_delete=models.SET_NULL,
        related_name="invitations",
        null=True,
        blank=True,
    )

    # Optional custom message from the inviter.
    message = models.TextField(blank=True, null=True)

    # Status of the invitation: pending, accepted, declined, expired.
    status = models.CharField(
        max_length=10,
        choices=InvitationStatus.CHOICES,
        default=InvitationStatus.PENDING,
    )

    # The organization to which the invitation applies.
    organization = models.ForeignKey(
        Organization, on_delete=models.CASCADE, related_name="invitations"
    )

    # Who sent the invitation (An admin user)
    invited_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="sent_invitations",
    )

    # The invitation expires after 7 days.
    expires_at = models.DateTimeField(default=get_expiry_date)

    def __str__(self):
        return f"Invitation for {self.email} to {self.organization.name}"

    def clean(self):
        """Validate that department is provided when role is Employee"""
        if self.role == Roles.EMPLOYEE and not self.department:
            raise ValidationError(
                {"department": "Department is required when role is Employee"}
            )
