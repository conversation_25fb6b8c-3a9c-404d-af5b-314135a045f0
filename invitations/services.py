import csv
import io
import logging
import uuid
from datetime import timedelta

from django.conf import settings
from django.db import transaction
from django.utils import timezone

from departments.models import Department
from organizations.models import Organization
from users.models import Roles, UserOrganization

from .models import Invitation, InvitationStatus
from .validators import InvitationValidator

logger = logging.getLogger(__name__)


class InvitationService:
    """Service for handling invitation operations"""

    # Constants
    MAX_FILE_SIZE = 1024 * 1024 * 2  # 2MB max file size
    BATCH_SIZE = 100  # Process records in batches

    def check_file_size(self, file):
        """Check if file size is within limits"""
        if file.size > self.MAX_FILE_SIZE:
            return (
                False,
                f"File size exceeds maximum allowed size of {self.MAX_FILE_SIZE/1024/1024}MB",
            )
        return True, None

    def validate_organization_access(self, organization_id, user):
        """Validate organization exists and user has access"""
        try:
            organization = Organization.objects.get(id=organization_id)

            # Check if user belongs to this organization
            if user.organization and user.organization.id != organization.id:
                return (
                    False,
                    "You can only invite users to your own organization",
                    organization,
                )

            return True, None, organization

        except Organization.DoesNotExist:
            return False, "Organization not found", None

    def process_csv_invitations(self, file, organization_id, invited_by):
        """Process CSV file containing invitation data"""
        try:
            # Check file size first
            is_valid_size, size_error = self.check_file_size(file)
            if not is_valid_size:
                return {"success": False, "message": size_error, "status_code": 400}

            # Parse and process CSV
            decoded_file = file.read().decode("utf-8")
            io_string = io.StringIO(decoded_file)
            reader = csv.DictReader(io_string)

            # Validate CSV structure
            expected_fields = {"email", "role"}
            if not reader.fieldnames or not expected_fields.issubset(
                set(reader.fieldnames)
            ):
                return {
                    "success": False,
                    "message": "CSV must contain email and role columns.",
                    "status_code": 400,
                }

            # Process the invitations
            return self._process_invitation_rows(reader, organization_id, invited_by)

        except UnicodeDecodeError:
            return {
                "success": False,
                "message": "File encoding error. Please ensure the CSV is UTF-8 encoded.",
                "status_code": 400,
            }
        except csv.Error as e:
            return {
                "success": False,
                "message": f"CSV parsing error: {str(e)}",
                "status_code": 400,
            }
        except Exception as e:
            logger.error(f"Error processing bulk invitations: {str(e)}")
            return {
                "success": False,
                "message": "An unexpected error occurred while processing the file",
                "status_code": 500,
            }

    def _process_invitation_rows(self, reader, organization_id, invited_by):
        """Process rows from CSV reader and create invitations in batches"""
        invitations_created = 0
        failed_invitations = []
        current_batch = []

        organization = Organization.objects.get(id=organization_id)

        # Add line counter
        line_number = 1

        # Process records in batches
        for row in reader:
            # Increment line number for each row
            line_number += 1

            email = row.get("email", "").strip().lower()
            role = row.get("role", "").strip().lower()
            department_id = row.get("department", "").strip()
            message = row.get("message", "").strip()

            # Validate data
            if not email:
                failed_invitations.append(
                    {
                        "row": dict(row),
                        "error": "Email is required",
                        "line": line_number,
                    }
                )
                continue

            # Validate email format
            is_valid_email, email_error = InvitationValidator.validate_email_format(
                email
            )
            if not is_valid_email:
                failed_invitations.append(
                    {
                        "email": email,
                        "error": f"Invalid email format: {email_error}",
                        "line": line_number,
                    }
                )
                continue

            # Validate role
            is_valid_role, role_error = InvitationValidator.validate_role(role)
            if not is_valid_role:
                failed_invitations.append(
                    {"email": email, "error": role_error, "line": line_number}
                )
                continue

            # Validate department if needed
            department = None
            if role in [Roles.ADMIN, Roles.EMPLOYEE] and department_id:
                try:
                    department = Department.objects.get(
                        id=department_id, organization=organization
                    )
                except Department.DoesNotExist:
                    failed_invitations.append(
                        {
                            "email": email,
                            "error": f"Department '{department_id}' does not exist",
                            "line": line_number,
                        }
                    )
                    continue

            elif role in [Roles.ADMIN, Roles.EMPLOYEE] and not department_id:
                failed_invitations.append(
                    {
                        "email": email,
                        "error": "Department is required for this role",
                        "line": line_number,
                    }
                )
                continue

            # Add to current batch for processing
            current_batch.append(
                {
                    "email": email,
                    "role": role,
                    "department": department,
                    "message": message,
                    "line": line_number,
                }
            )

            # Process in batches to improve performance
            if len(current_batch) >= self.BATCH_SIZE:
                batch_results = self.create_invitations_batch(
                    current_batch, organization_id, invited_by
                )
                invitations_created += batch_results["created"]
                failed_invitations.extend(batch_results["failed"])
                current_batch = []

        # Process any remaining records
        if current_batch:
            batch_results = self.create_invitations_batch(
                current_batch, organization_id, invited_by
            )
            invitations_created += batch_results["created"]
            failed_invitations.extend(batch_results["failed"])

        return {
            "success": True,
            "data": {
                "total_processed": invitations_created + len(failed_invitations),
                "successful_invitations": invitations_created,
                "failed_invitations": failed_invitations,
                "created_at": timezone.now().isoformat(),
            },
            "status_code": 201,
        }

    @transaction.atomic
    def create_invitations_batch(self, batch, organization_id, invited_by):
        """Create a batch of invitations within a transaction"""
        created_count = 0
        failed_records = []

        # Get existing invitations and members to avoid redundant lookups
        existing_emails = self._get_existing_emails(batch, organization_id)
        existing_users = self._get_existing_users(batch, organization_id)

        for item in batch:
            email = item["email"]
            role = item["role"]
            department = item.get("department")
            message = item.get("message")
            line_number = item.get("line")

            # Check if already a member
            if email in existing_users:
                failed_records.append(
                    {
                        "email": email,
                        "error": "Already a member of this organization",
                        "line": line_number,
                    }
                )
                continue

            # Check if already invited
            if email in existing_emails:
                failed_records.append(
                    {
                        "email": email,
                        "error": "Already invited to this organization",
                        "line": line_number,
                    }
                )
                continue

            # Create new invitation
            invitation = self._create_invitation(
                email, role, department, message, organization_id, invited_by
            )
            created_count += 1

            # TODO: Send invitation email

        return {"created": created_count, "failed": failed_records}

    def _get_existing_emails(self, batch, organization_id):
        """Get existing invitations for this organization"""
        return set(
            Invitation.objects.filter(
                organization_id=organization_id,
                email__in=[item["email"] for item in batch],
                status=InvitationStatus.PENDING,
            ).values_list("email", flat=True)
        )

    def _get_existing_users(self, batch, organization_id):
        """Get existing users in this organization"""
        # Get users with organizations related to this organization
        users_in_org = UserOrganization.objects.filter(
            organization_id=organization_id
        ).values_list("user__email", flat=True)

        return set(users_in_org)

    def _create_invitation(
        self, email, role, department, message, organization_id, invited_by
    ):
        """Create a single invitation"""
        invitation = Invitation.objects.create(
            organization_id=organization_id,
            email=email,
            role=role,
            department=department,
            message=message,
            invited_by=invited_by,
            status=InvitationStatus.PENDING,
        )

        return invitation
