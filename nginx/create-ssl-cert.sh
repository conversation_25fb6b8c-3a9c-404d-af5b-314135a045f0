#!/bin/bash

# Script: create-ssl-cert.sh
# Purpose: Automates the process of obtaining and installing SSL certificates using Certbot for Nginx
# Requirements:
#   - Ubuntu/Debian-based system
#   - Sudo privileges
#   - Ngin<PERSON> installed and configured
#   - Domain name pointing to the server
#
# Usage: ./create-ssl-cert.sh yourdomain.com
# Example: ./create-ssl-cert.sh example.com
#
# This script will:
# 1. Install Certbot and the Nginx plugin
# 2. Obtain and install an SSL certificate for the specified domain
# 3. Automatically configure Nginx to use the certificate

# Install Certbot and Nginx plugin
sudo apt install certbot python3-certbot-nginx -y

# Obtain and install SSL certificate for the specified domain
sudo certbot --nginx -d api.jechspace.com
