from django.apps import AppConfig


class EventsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'events'

    def ready(self):
        # Import events module to ensure it's loaded
        import events.events
        
        # Auto-discover all event listeners in installed apps
        from events.events import autodiscover_listeners
        autodiscover_listeners()
