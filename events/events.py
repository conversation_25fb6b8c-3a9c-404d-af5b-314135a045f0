from django.dispatch import Signal
from typing import Any, Callable


class EventEmitter:
  """
    A simple event emitter class that mimics NestJS event system
  """
  _instance = None

  def __new__(cls):
    if cls._instance is None:
      cls._instance = super(EventEmitter, cls).__new__(cls)
      cls._instance._listeners = {}
      cls._instance._signal = Signal()
    return cls._instance

  def emit(self, event_name: str, payload: Any = None) -> None:
    """
    Emit an event with optional payload

    Args:
        event_name: Name of the event to emit
        payload: Data to pass to listeners
    """
    self._signal.send(sender=self.__class__, event_name=event_name, payload=payload)

  def on(self, event_name: str) -> Callable:
    """
    Decorator for registering event listeners

    Args:
        event_name: Name of the event to listen for

    Example:
        @events.on('user.created')
        def handle_user_created(payload):
            # Do something with payload
    """
    def decorator(func):
      if event_name not in self._listeners:
          self._listeners[event_name] = []
      self._listeners[event_name].append(func)

      # Connect to signal if not already connected
      if not self._signal.has_listeners(self._handle_event):
          self._signal.connect(self._handle_event)

      return func
    return decorator

  def _handle_event(self, sender, event_name, payload, **kwargs):
    """
    Internal handler for events
    """
    if event_name in self._listeners:
      for listener in self._listeners[event_name]:
        listener(payload)

# Create singleton instance
events = EventEmitter()

# For use in apps.py and other modules that need all listeners to be imported
def autodiscover_listeners():
  """
  Auto-discover event listeners in installed apps
  """
  from importlib import import_module
  from django.apps import apps

  for app_config in apps.get_app_configs():
    # Try to import the listeners module
    try:
      import_module(f"{app_config.name}.listeners")
    except ImportError:
      pass




