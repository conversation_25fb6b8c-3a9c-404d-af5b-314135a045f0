from rest_framework.pagination import PageNumberPagination
from rest_framework.response import Response
from django.utils.translation import gettext as _
from typing import Optional, Any, Dict
from jechspace_backend.utils import api_response


class CustomPageNumberPagination(PageNumberPagination):
    """
    Enhanced pagination class for API responses with consistent formatting
    and additional metadata.

    Features:
    - Configurable page size with reasonable defaults
    - Consistent response format across all endpoints
    - Support for custom ordering
    - Enhanced metadata in pagination response
    - Automatic pluralization of resource names
    - Support for custom message formatting

    Usage:
        class MyViewSet(viewsets.ModelViewSet):
            pagination_class = CustomPageNumberPagination
            ordering_fields = ['name', 'created_at']  # Optional: specify allowed ordering fields
            ordering = ['-created_at']  # Optional: default ordering
    """
    page_size = 20
    page_size_query_param = "limit"
    max_page_size = 100
    items_key = "items"
    ordering_param = "ordering"  # Parameter for custom ordering

    def get_page_size(self, request) -> int:
        """
        Get the page size from the request, respecting max_page_size.
        Falls back to settings.PAGE_SIZE if not specified.
        """
        try:
            page_size = int(request.query_params.get(self.page_size_query_param, self.page_size))
            return min(page_size, self.max_page_size)
        except (TypeError, ValueError):
            return self.page_size

    def get_ordering(self, request, queryset, view) -> Optional[str]:
        """
        Get the ordering parameter from the request if allowed.
        Falls back to view.ordering if specified.

        Returns:
            Optional[str]: The ordering parameter as a string, or None
        """
        # First try to get ordering from request
        ordering = request.query_params.get(self.ordering_param)
        if ordering and hasattr(view, 'ordering_fields') and ordering.lstrip('-') in view.ordering_fields:
            return ordering

        # If no ordering in request, use view's default ordering
        if hasattr(view, 'ordering'):
            # If view.ordering is a list, take the first item
            if isinstance(view.ordering, (list, tuple)):
                return view.ordering[0]
            return view.ordering

        return None

    def paginate_queryset(self, queryset, request, view=None):
        """
        Paginate the queryset with support for custom ordering.
        Store the view reference for use in get_paginated_response.
        """
        # Apply ordering if specified
        ordering = self.get_ordering(request, queryset, view)
        if ordering:
            # Handle both string and list ordering
            if isinstance(ordering, (list, tuple)):
                queryset = queryset.order_by(*ordering)
            else:
                queryset = queryset.order_by(ordering)

        return super().paginate_queryset(queryset, request, view)

    def get_paginated_response(self, data, message=None):
        """
        Return a paginated response with enhanced metadata.

        Args:
            data: The paginated data
            message: Optional custom message. If not provided, will default message

        Returns:
            Response with consistent format including pagination metadata
        """
        if message is None:
            message = "Items retrieved successfully"  # Fallback message

        return api_response(
            message=message,
            data={
                self.items_key: data,
                "pagination": {
                    "total": self.page.paginator.count,
                    "pages": self.page.paginator.num_pages,
                    "current_page": self.page.number,
                    "limit": self.get_page_size(self.request),
                    "next": self.get_next_link(),
                    "previous": self.get_previous_link(),
                    "has_next": self.page.has_next(),
                    "has_previous": self.page.has_previous(),
                }
            }
        )

    def get_schema_operation_parameters(self, view) -> list:
        """
        Return OpenAPI parameters for pagination and ordering.
        """
        parameters = [
            {
                'name': self.page_query_param,
                'required': False,
                'in': 'query',
                'description': 'Page number',
                'schema': {'type': 'integer', 'minimum': 1}
            },
            {
                'name': self.page_size_query_param,
                'required': False,
                'in': 'query',
                'description': f'Number of results per page (max: {self.max_page_size})',
                'schema': {'type': 'integer', 'minimum': 1, 'maximum': self.max_page_size}
            }
        ]

        # Add ordering parameter if view supports it
        if hasattr(view, 'ordering_fields'):
            parameters.append({
                'name': self.ordering_param,
                'required': False,
                'in': 'query',
                'description': f'Order by field (allowed: {", ".join(view.ordering_fields)})',
                'schema': {'type': 'string'}
            })

        return parameters
