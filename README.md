# JechSpace Backend

[![Python Version](https://img.shields.io/badge/python-3.12-blue.svg)](https://www.python.org/downloads/)
[![Django Version](https://img.shields.io/badge/django-5.1-green.svg)](https://www.djangoproject.com/)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

JechSpace is a modern workspace management platform that enables organizations to efficiently manage their physical spaces, bookings, and resources. This repository contains the backend service built with Django and Django REST Framework.

## 🌟 Features

- **Authentication & Authorization**
  - JWT-based authentication
  - Role-based access control
  - Multi-tenant support

- **Workspace Management**
  - Dynamic workspace creation and configuration
  - Real-time availability tracking
  - Advanced booking system

- **Organization Features**
  - Department management
  - User management
  - Custom permission system

- **Event Management**
  - Event creation and scheduling
  - Calendar integration
  - Automated notifications

- **Location Services**
  - Multi-location support
  - Location-based resource allocation
  - Geographic data management

## 🚀 Quick Start

```bash
# Clone the repository
git clone https://github.com/your-org/jechspace-backend.git
cd jechspace-backend

# Create and activate virtual environment
python -m venv .venv
source .venv/bin/activate  # On Windows: .venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Run migrations
python manage.py migrate

# Start the development server
python manage.py runserver
```

## 📚 Documentation

- [API Documentation](http://localhost:8000/api/docs/)
- [Development Guide](DEVELOPMENT.md)
- [API Reference](docs/api.md)

## 🛠 Tech Stack

- **Backend Framework:** Django 5.1
- **API Framework:** Django REST Framework
- **Database:** mysql
- **Task Queue:** Celery
- **Cache:** Redis
- **Testing:** pytest
- **Code Quality:** flake8, black, isort
- **Documentation:** drf-spectacular

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.


## 📞 Support

For support, email <EMAIL>
