from rest_framework import status
from rest_framework.response import Response


def api_response(
    data=None,
    errors=None,
    message="",
    status_code=status.HTTP_200_OK,
    success=True,
):
    """
    Standardized API response format

    Args:
      data: The data to return (if any)
      errors: Error details (if any)
      message: A human-readable message
      status_code: The HTTP status code
      success: Whether the request was successful
      error_code: Optional error code for frontend handling

    Returns:
      Response object with standardized format
    """
    response_status = "success" if success else "error"

    response_data = {
        "status": response_status,
        "message": message,
    }

    if data is not None:
        response_data["data"] = data

    if errors is not None:
        # Create the standardized errors structure
        standardized_errors = {}

        # Handle different error formats and convert to standardized format
        if isinstance(errors, dict):
            for field, field_errors in errors.items():
                # Handle case where field_errors is a list of errors
                if isinstance(field_errors, list) and field_errors:
                    err_msg = field_errors[0]
                    err_code = None

                    # Extract error code if available
                    if hasattr(field_errors[0], "code"):
                        err_code = field_errors[0].code

                    standardized_errors[field] = {
                        "code": err_code,
                        "message": str(err_msg),
                    }
                # Handle case where field_errors is a dictionary with "code" key
                elif field == "code" and isinstance(field_errors, str):
                    # This is a special case where errors = {"code": ERROR_CODE}
                    # We need to add this to a generic error
                    standardized_errors["error"] = {
                        "code": field_errors,
                        "message": message or "An error occurred",
                    }
                # Handle case where field_errors is a direct serializer ValidationError with code attr
                elif hasattr(field_errors, "code"):
                    standardized_errors[field] = {
                        "code": field_errors.code,
                        "message": str(field_errors),
                    }
                # Handle case where field_errors is already in the correct format
                elif (
                    isinstance(field_errors, dict)
                    and "code" in field_errors
                    and "message" in field_errors
                ):
                    standardized_errors[field] = field_errors
                # Handle case where field_errors is a simple string or other value
                else:
                    standardized_errors[field] = {
                        "code": None,
                        "message": str(field_errors),
                    }

        response_data["errors"] = standardized_errors

    return Response(response_data, status=status_code)


class ApiErrorException(Exception):
    """Custom API error exception with status code and message"""

    def __init__(self, message, status_code=status.HTTP_400_BAD_REQUEST, code=None):
        self.message = message
        self.status_code = status_code
        self.code = code
        super().__init__(self.message)


def handle_exception(exc, context):
    """
    Custom exception handler for DRF

    To use, add to REST_FRAMEWORK settings:
    'EXCEPTION_HANDLER': 'jechspace_backend.utils.handle_exception'
    """
    if isinstance(exc, ApiErrorException):
        errors = None
        if exc.code:
            errors = {"error": {"code": exc.code, "message": exc.message}}

        return api_response(
            message=exc.message,
            status_code=exc.status_code,
            success=False,
            errors=errors,
        )

    # Default to the standard DRF exception handler
    from rest_framework.views import exception_handler

    return exception_handler(exc, context)


class ErrorCodes:
    # Authentication Errors
    INVALID_CREDENTIALS = "INVALID_CREDENTIALS"
    ACCOUNT_NOT_VERIFIED = "ACCOUNT_NOT_VERIFIED"
    ACCOUNT_ALREADY_VERIFIED = "ACCOUNT_ALREADY_VERIFIED"
    MISSING_CREDENTIALS = "MISSING_CREDENTIALS"
    INVALID_TOKEN = "INVALID_TOKEN"
    EXPIRED_TOKEN = "EXPIRED_TOKEN"
    USER_NOT_FOUND = "USER_NOT_FOUND"
    INVALID_ACTION = "INVALID_ACTION"

    # Organization Errors
    ORGANIZATION_NOT_FOUND = "ORGANIZATION_NOT_FOUND"
    ORGANIZATION_REQUIRED = "ORGANIZATION_REQUIRED"
    SLUG_ALREADY_TAKEN = "SLUG_ALREADY_TAKEN"
    USER_NOT_IN_ORGANIZATION = "USER_NOT_IN_ORGANIZATION"
    ORGANIZATION_SLUG_EXISTS = "ORGANIZATION_SLUG_EXISTS"
    ORGANIZATION_NAME_REQUIRED = "ORGANIZATION_NAME_REQUIRED"
    COMMON_EMAIL_DOMAIN = "COMMON_EMAIL_DOMAIN"
    ORGANIZATION_INDUSTRY_REQUIRED = "ORGANIZATION_INDUSTRY_REQUIRED"
    INDIVIDUAL_CANNOT_CREATE_ORGANIZATION = "INDIVIDUAL_CANNOT_CREATE_ORGANIZATION"
    CANNOT_REMOVE_ORGANIZATION_OWNER = "CANNOT_REMOVE_ORGANIZATION_OWNER"
    CANNOT_ASSIGN_OWNER_ROLE = "CANNOT_ASSIGN_OWNER_ROLE"
    USER_ALREADY_OWNS_AN_ORGANIZATION = "USER_ALREADY_OWNS_AN_ORGANIZATION"
    CANNOT_MODIFY_ORGANIZATION_OWNER = "CANNOT_MODIFY_ORGANIZATION_OWNER"

    # User Registration Errors
    EMAIL_ALREADY_EXISTS = "EMAIL_ALREADY_EXISTS"
    EMAIL_DOMAIN_NOT_ALLOWED = "EMAIL_DOMAIN_NOT_ALLOWED"
    EMAIL_ALREADY_ORGANIZATION_OWNER = "EMAIL_ALREADY_ORGANIZATION_OWNER"
    EMAIL_NO_ORGANIZATION = "EMAIL_NO_ORGANIZATION"
    PROFESSION_FIELD_MISSING = "PROFESSION_FIELD_MISSING"
    DEPARTMENT_FIELD_MISSING = "DEPARTMENT_FIELD_MISSING"
    PASSWORDS_DO_NOT_MATCH = "PASSWORDS_DO_NOT_MATCH"

    # Invitation Errors
    INVITATION_ALREADY_SENT = "INVITATION_ALREADY_SENT"
    INVITATION_USER_ALREADY_MEMBER = "INVITATION_USER_ALREADY_MEMBER"
    USER_NOT_INVITED = "USER_NOT_INVITED"
    JOIN_REQUEST_NOT_FOUND = "JOIN_REQUEST_NOT_FOUND"
    JOIN_REQUEST_ALREADY_ACCEPTED = "JOIN_REQUEST_ALREADY_ACCEPTED"
    JOIN_REQUEST_ALREADY_EXISTS = "JOIN_REQUEST_ALREADY_EXISTS"
    USER_ALREADY_IN_ORGANIZATION = "USER_ALREADY_IN_ORGANIZATION"
    INVITATION_EXPIRED = "INVITATION_EXPIRED"
    DEPARTMENT_REQUIRED = "DEPARTMENT_REQUIRED"
    INVALID_ROLE = "INVALID_ROLE"
    CANNOT_INVITE_OWNER = "CANNOT_INVITE_OWNER"
    INVITATION_NOT_FOR_USER = "INVITATION_NOT_FOR_USER"
    INVITATION_CANCELLED = "INVITATION_CANCELLED"
    INVITATION_ALREADY_PROCESSED = "INVITATION_ALREADY_PROCESSED"

    # Password Validation Errors
    PASSWORD_TOO_SHORT = "PASSWORD_TOO_SHORT"
    PASSWORD_NO_UPPERCASE = "PASSWORD_NO_UPPERCASE"
    PASSWORD_NO_LOWERCASE = "PASSWORD_NO_LOWERCASE"
    PASSWORD_NO_DIGIT = "PASSWORD_NO_DIGIT"
    PASSWORD_NO_SPECIAL = "PASSWORD_NO_SPECIAL"
    INVALID_PASSWORD = "INVALID_PASSWORD"

    # Permission Errors
    PERMISSION_DENIED = "PERMISSION_DENIED"
    PERMISSION_CONFIG_MISSING = "PERMISSION_CONFIG_MISSING"
    INSUFFICIENT_PERMISSIONS = "INSUFFICIENT_PERMISSIONS"
    DEPARTMENT_PERMISSION_DENIED = "DEPARTMENT_PERMISSION_DENIED"
    DEPARTMENT_PERMISSION_NOT_FOUND = "DEPARTMENT_PERMISSION_NOT_FOUND"
    MEMBER_PERMISSION_DENIED = "MEMBER_PERMISSION_DENIED"
    OBJECT_PERMISSION_DENIED = "OBJECT_PERMISSION_DENIED"

    # General Errors
    REQUIRED_FIELD_MISSING = "REQUIRED_FIELD_MISSING"
    INVALID_FORMAT = "INVALID_FORMAT"
    EMAIL_NOT_FOUND = "EMAIL_NOT_FOUND"

    # Location and Spaces errors
    LOCATION_NOT_FOUND = "LOCATION_NOT_FOUND"
    LOCATION_POLICY_NOT_FOUND = "LOCATION_POLICY_NOT_FOUND"
    DESK_NUMBER_REQUIRED = "DESK_NUMBER_REQUIRED"
    ROOM_NUMBER_REQUIRED = "ROOM_NUMBER_REQUIRED"
    LOCATION_REQUIRED = "LOCATION_REQUIRED"
    SPACE_NOT_FOUND = "SPACE_NOT_FOUND"

    # Booking error
    DESK_BOOKING_SINGLE_USER_ONLY = "DESK_BOOKING_SINGLE_USER_ONLY"
    MIN_DURATION_EXCEEDS_MAX = "MIN_DURATION_EXCEEDS_MAX"
    NEGATIVE_BOOKING_INTERVAL = "NEGATIVE_BOOKING_INTERVAL"
    NEGATIVE_ADVANCE_BOOKING = "NEGATIVE_ADVANCE_BOOKING"
    ATTENDEES_COUNT_MISMATCH = "ATTENDEES_COUNT_MISMATCH"
    SPACE_ALREADY_BOOKED = "SPACE_ALREADY_BOOKED"
    SPACE_NOT_AVAILABLE = "SPACE_NOT_AVAILABLE"
    USER_ALREADY_BOOKED = "USER_ALREADY_BOOKED"
