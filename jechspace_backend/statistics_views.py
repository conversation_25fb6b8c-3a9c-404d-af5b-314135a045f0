"""
Statistics and analytics views for JechSpace API.
Provides comprehensive statistics for different user types and scenarios.
"""

from django.contrib.auth import get_user_model
from django.db.models import Count, Q
from django.utils import timezone
from datetime import timedelta

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework import status
from rest_framework.permissions import IsA<PERSON>enticated, IsAdminUser
from rest_framework.views import APIView

from jechspace_backend.utils import api_response
from organizations.models import Organization
from users.models import UserType, UserOrganization
from docs.schemas import (
    SUCCESS_RESPONSE_SCHEMA,
    AUTHENTICATION_ERROR_RESPONSE,
    PERMISSION_ERROR_RESPONSE
)
from docs.examples import (
    ALL_STATS_RESPONSE,
    ORGANIZATION_ONLY_STATS_RESPONSE,
    INDIVIDUAL_STATS_RESPONSE
)

User = get_user_model()


class GlobalStatsView(APIView):
    """
    Global platform statistics - Admin only
    """
    permission_classes = [IsAdminUser]

    @swagger_auto_schema(
        operation_summary="Get global platform statistics",
        operation_description="""
        Retrieve comprehensive statistics for the entire JechSpace platform.
        
        **Admin Only Endpoint**
        
        **Statistics Include:**
        - Total users, organizations, spaces, and bookings
        - User type breakdown (individual vs organization owners)
        - Organization size distribution
        - Space utilization metrics
        - Booking trends and patterns
        - Growth metrics over time
        
        **Use Cases:**
        - Platform analytics dashboard
        - Business intelligence reporting
        - Performance monitoring
        - Growth tracking
        """,
        responses={
            200: openapi.Response(
                description="Global statistics retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'status': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, example='Statistics retrieved successfully'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'overview': openapi.Schema(
                                    type=openapi.TYPE_OBJECT,
                                    properties={
                                        'total_users': openapi.Schema(type=openapi.TYPE_INTEGER, example=1250),
                                        'total_organizations': openapi.Schema(type=openapi.TYPE_INTEGER, example=45),
                                        'total_spaces': openapi.Schema(type=openapi.TYPE_INTEGER, example=320),
                                        'total_bookings': openapi.Schema(type=openapi.TYPE_INTEGER, example=8750),
                                        'active_bookings': openapi.Schema(type=openapi.TYPE_INTEGER, example=125),
                                    }
                                ),
                                'user_stats': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'organization_stats': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'space_stats': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'booking_stats': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'period': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
                            }
                        )
                    }
                ),
                examples={
                    'application/json': ALL_STATS_RESPONSE
                }
            ),
            401: AUTHENTICATION_ERROR_RESPONSE,
            403: PERMISSION_ERROR_RESPONSE,
        },
        tags=['Statistics'],
    )
    def get(self, request):
        """Get comprehensive platform statistics"""
        try:
            # Import models here to avoid circular imports
            from spaces.models import Space
            from bookings.models import Booking
            
            # Overview statistics
            total_users = User.objects.count()
            total_organizations = Organization.objects.count()
            total_spaces = Space.objects.count()
            total_bookings = Booking.objects.count()
            active_bookings = Booking.objects.filter(
                status__in=['pending', 'approved'],
                start_time__lte=timezone.now(),
                end_time__gte=timezone.now()
            ).count()

            # User statistics
            individual_users = User.objects.filter(user_type=UserType.INDIVIDUAL).count()
            organization_owners = User.objects.filter(user_type=UserType.ORGANIZATION_OWNER).count()
            verified_users = User.objects.filter(is_verified=True).count()
            unverified_users = User.objects.filter(is_verified=False).count()

            # Organization statistics
            private_orgs = Organization.objects.filter(type='private').count()
            public_orgs = Organization.objects.filter(type='public').count()
            
            # Organization size distribution
            org_sizes = Organization.objects.values('employee_size').annotate(count=Count('id'))
            org_size_dist = {item['employee_size'] or 'unknown': item['count'] for item in org_sizes}

            # Space statistics
            available_spaces = Space.objects.filter(status='available').count()
            occupied_spaces = Space.objects.filter(status='occupied').count()
            maintenance_spaces = Space.objects.filter(status='maintenance').count()
            
            # Space type distribution
            space_types = Space.objects.values('space_type').annotate(count=Count('id'))
            space_type_dist = {item['space_type']: item['count'] for item in space_types}

            # Booking statistics
            booking_statuses = Booking.objects.values('status').annotate(count=Count('id'))
            booking_status_dist = {item['status']: item['count'] for item in booking_statuses}
            
            # Recent booking trends
            now = timezone.now()
            this_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            last_month_start = (this_month_start - timedelta(days=1)).replace(day=1)
            
            bookings_this_month = Booking.objects.filter(created_at__gte=this_month_start).count()
            bookings_last_month = Booking.objects.filter(
                created_at__gte=last_month_start,
                created_at__lt=this_month_start
            ).count()

            stats_data = {
                'overview': {
                    'total_users': total_users,
                    'total_organizations': total_organizations,
                    'total_spaces': total_spaces,
                    'total_bookings': total_bookings,
                    'active_bookings': active_bookings,
                },
                'user_stats': {
                    'individual_users': individual_users,
                    'organization_owners': organization_owners,
                    'verified_users': verified_users,
                    'unverified_users': unverified_users,
                },
                'organization_stats': {
                    'private_organizations': private_orgs,
                    'public_organizations': public_orgs,
                    'organizations_by_size': org_size_dist,
                },
                'space_stats': {
                    'available_spaces': available_spaces,
                    'occupied_spaces': occupied_spaces,
                    'maintenance_spaces': maintenance_spaces,
                    'spaces_by_type': space_type_dist,
                },
                'booking_stats': {
                    **booking_status_dist,
                    'bookings_this_month': bookings_this_month,
                    'bookings_last_month': bookings_last_month,
                },
                'period': f"{timezone.now().date()}T00:00:00Z to {timezone.now().date()}T23:59:59Z"
            }

            return api_response(
                data=stats_data,
                message="Statistics retrieved successfully",
                status_code=status.HTTP_200_OK
            )

        except Exception as e:
            return api_response(
                success=False,
                errors={'error': str(e)},
                message="Failed to retrieve statistics",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class UserStatsView(APIView):
    """
    Individual user statistics
    """
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_summary="Get user statistics",
        operation_description="""
        Retrieve statistics for the authenticated user.
        
        **Statistics Include:**
        - Personal booking history and patterns
        - Organization memberships and roles
        - Usage patterns and preferences
        - Activity trends over time
        
        **Use Cases:**
        - Personal dashboard
        - User profile analytics
        - Usage insights
        - Activity tracking
        """,
        responses={
            200: openapi.Response(
                description="User statistics retrieved successfully",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'status': openapi.Schema(type=openapi.TYPE_STRING, example='success'),
                        'message': openapi.Schema(type=openapi.TYPE_STRING, example='User statistics retrieved successfully'),
                        'data': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'user_id': openapi.Schema(type=openapi.TYPE_STRING, format='uuid'),
                                'user_type': openapi.Schema(type=openapi.TYPE_STRING),
                                'overview': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'booking_stats': openapi.Schema(type=openapi.TYPE_OBJECT),
                                'organization_memberships': openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Schema(type=openapi.TYPE_OBJECT)),
                                'period': openapi.Schema(type=openapi.TYPE_STRING, format='date-time'),
                            }
                        )
                    }
                ),
                examples={
                    'application/json': INDIVIDUAL_STATS_RESPONSE
                }
            ),
            401: AUTHENTICATION_ERROR_RESPONSE,
        },
        tags=['Statistics'],
    )
    def get(self, request):
        """Get statistics for the authenticated user"""
        try:
            from bookings.models import Booking
            
            user = request.user
            
            # User's bookings
            user_bookings = Booking.objects.filter(user=user)
            total_bookings = user_bookings.count()
            active_bookings = user_bookings.filter(
                status__in=['pending', 'approved'],
                start_time__lte=timezone.now(),
                end_time__gte=timezone.now()
            ).count()
            completed_bookings = user_bookings.filter(status='completed').count()
            cancelled_bookings = user_bookings.filter(status='cancelled').count()
            
            # Organization memberships
            user_orgs = UserOrganization.objects.filter(user=user).select_related('organization', 'department')
            organizations_joined = user_orgs.count()
            
            # Recent booking trends
            now = timezone.now()
            this_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            last_month_start = (this_month_start - timedelta(days=1)).replace(day=1)
            
            bookings_this_month = user_bookings.filter(created_at__gte=this_month_start).count()
            bookings_last_month = user_bookings.filter(
                created_at__gte=last_month_start,
                created_at__lt=this_month_start
            ).count()

            stats_data = {
                'user_id': str(user.id),
                'user_type': user.user_type,
                'overview': {
                    'total_bookings': total_bookings,
                    'active_bookings': active_bookings,
                    'completed_bookings': completed_bookings,
                    'cancelled_bookings': cancelled_bookings,
                    'organizations_joined': organizations_joined,
                },
                'booking_stats': {
                    'bookings_this_month': bookings_this_month,
                    'bookings_last_month': bookings_last_month,
                },
                'organization_memberships': [
                    {
                        'organization_id': str(user_org.organization.id),
                        'organization_name': user_org.organization.name,
                        'role': user_org.role,
                        'department': user_org.department.name if user_org.department else None,
                        'joined_date': user_org.created_at.isoformat(),
                    }
                    for user_org in user_orgs
                ],
                'period': f"{timezone.now().date()}T00:00:00Z to {timezone.now().date()}T23:59:59Z"
            }

            return api_response(
                data=stats_data,
                message="User statistics retrieved successfully",
                status_code=status.HTTP_200_OK
            )

        except Exception as e:
            return api_response(
                success=False,
                errors={'error': str(e)},
                message="Failed to retrieve user statistics",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
