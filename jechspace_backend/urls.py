"""
URL configuration for authenticator project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import include, path

from drf_yasg import openapi
from drf_yasg.views import get_schema_view
from rest_framework import permissions

from .views import welcome
from .statistics_views import GlobalStatsView, UserStatsView

schema_view = get_schema_view(
    openapi.Info(
        title="JechSpace API Documentation",
        default_version="v1",
        description="""
# JechSpace Workspace Management API

JechSpace is a comprehensive workspace management platform that enables organizations to efficiently manage their physical spaces, bookings, and resources.

## Features

- **Authentication & Authorization**: JWT-based authentication with role-based access control
- **Multi-tenant Support**: Organization-based workspace management
- **Space Management**: Create and manage different types of workspaces
- **Booking System**: Advanced booking system with approval workflows
- **Location Management**: Manage multiple office locations
- **User Management**: Individual and organization user types
- **Invitation System**: Invite users to join organizations
- **Real-time Notifications**: Email notifications for important events

## Authentication

This API uses JWT (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-access-token>
```

## User Types

- **Individual**: Personal users who can book spaces
- **Organization Owner**: Users who can create and manage organizations
- **Organization Members**: Users who belong to organizations with various roles

## Error Handling

All API responses follow a consistent format:

```json
{
    "status": "success|error",
    "message": "Human readable message",
    "data": {},
    "errors": {}
}
```

## Rate Limiting

API requests are rate-limited to ensure fair usage. Check response headers for rate limit information.
        """,
        terms_of_service="https://jechspace.com/terms/",
        contact=openapi.Contact(
            name="JechSpace API Support",
            email="<EMAIL>",
            url="https://jechspace.com/support"
        ),
        license=openapi.License(
            name="MIT License",
            url="https://opensource.org/licenses/MIT"
        ),
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

urlpatterns = [
    # Welcome endpoints
    path("", welcome, name="welcome"),
    path("api/v1/", welcome, name="api-welcome"),
    path("health/", welcome, name="health"),
    path("admin/", admin.site.urls),
    # API endpoints with versioning
    path("api/v1/auth/", include("authentication.urls")),
    path("api/v1/organizations/", include("organizations.urls")),
    path("api/v1/", include("invitations.urls")),
    # path("api/v1/organizations/", include("locations.urls")),
    path("api/v1/organizations/", include("join_requests.urls")),
    path("api/v1/spaces/", include("spaces.urls")),
    path("api/v1/locations/", include("locations.urls")),
    path("api/v1/bookings/", include("bookings.urls")),
    path("api/v1/users/", include("users.urls")),
    # Statistics endpoints
    path("api/v1/stats/global/", GlobalStatsView.as_view(), name="global-stats"),
    path("api/v1/stats/user/", UserStatsView.as_view(), name="user-stats"),
    path(
        "swagger<format>/", schema_view.without_ui(cache_timeout=0), name="schema_json"
    ),
    path(
        "swagger/",
        schema_view.with_ui("swagger", cache_timeout=0),
        name="schema-swagger-ui",
    ),
    path("redoc/", schema_view.with_ui("redoc", cache_timeout=0), name="schema-redoc"),
]
