import json

from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin


class JSONErrorMiddleware(MiddlewareMixin):
    """
    Middleware to ensure that all API responses are in JSON format,
    even in case of errors or exceptions that might trigger Django's HTML error pages.
    """

    def process_response(self, request, response):
        """
        Process the response to ensure it's in JSON format if it's an error.
        """
        # Check if the response is an error (status code >= 400)
        if response.status_code >= 400:
            content_type = response.get("Content-Type", "")

            # If the content type is HTML, convert it to JSON
            if "text/html" in content_type:
                error_message = "An error occurred"

                # Try to extract error message from the HTML
                if hasattr(response, "content"):
                    try:
                        response_content = response.content.decode("utf-8")
                        # Very simple extraction, just to get some indication of the error
                        if (
                            "<title>" in response_content
                            and "</title>" in response_content
                        ):
                            title_start = response_content.find("<title>") + 7
                            title_end = response_content.find("</title>")
                            if title_start > 0 and title_end > title_start:
                                error_message = response_content[
                                    title_start:title_end
                                ].strip()
                    except (UnicodeDecodeError, AttributeError):
                        pass

                # Create a JSON response with the standardized format
                json_response = JsonResponse(
                    {
                        "status": "error",
                        "message": error_message,
                        "errors": {
                            "error": {"code": "UNKNOWN_ERROR", "message": error_message}
                        },
                    },
                    status=response.status_code,
                )

                return json_response

        return response
